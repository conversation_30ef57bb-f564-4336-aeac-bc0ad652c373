#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Customer management for Qatar POS System
"""

import sqlite3
from datetime import datetime
from typing import List, Optional, Dict, Any

from database.models import Customer
from database.database_manager import DatabaseManager
from core.auth import auth_manager
from utils.logger import get_logger

class CustomerManager:
    """Customer management class"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.logger = get_logger()
        
    def create_customer(self, customer: Customer) -> Optional[int]:
        """Create a new customer"""
        try:
            if not auth_manager.can_access_feature('customers'):
                self.logger.warning("Unauthorized access to customer creation")
                return None
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO customers (
                        name, phone, email, national_id, address, credit_limit
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    customer.name, customer.phone, customer.email,
                    customer.national_id, customer.address, customer.credit_limit
                ))
                
                customer_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"Customer created: {customer.name} (ID: {customer_id})")
                return customer_id
                
        except sqlite3.IntegrityError as e:
            self.logger.error(f"Customer creation failed: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error creating customer: {e}")
            return None
            
    def update_customer(self, customer_id: int, updates: Dict[str, Any]) -> bool:
        """Update customer information"""
        try:
            if not auth_manager.can_access_feature('customers'):
                self.logger.warning("Unauthorized access to customer update")
                return False
                
            # Build update query dynamically
            set_clauses = []
            values = []
            
            for field, value in updates.items():
                if field in ['name', 'phone', 'email', 'national_id', 'address', 
                           'credit_limit', 'current_balance', 'is_active']:
                    set_clauses.append(f"{field} = ?")
                    values.append(value)
                    
            if not set_clauses:
                return False
                
            values.append(customer_id)
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                query = f"UPDATE customers SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(query, values)
                
                conn.commit()
                
                if cursor.rowcount > 0:
                    self.logger.info(f"Customer updated: ID {customer_id}")
                    return True
                else:
                    self.logger.warning(f"Customer not found: ID {customer_id}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error updating customer: {e}")
            return False
            
    def get_customer_by_id(self, customer_id: int) -> Optional[Dict[str, Any]]:
        """Get customer by ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM customers WHERE id = ?", (customer_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
                
        except Exception as e:
            self.logger.error(f"Error fetching customer: {e}")
            return None
            
    def get_customer_by_phone(self, phone: str) -> Optional[Dict[str, Any]]:
        """Get customer by phone number"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM customers 
                    WHERE phone = ? AND is_active = 1
                """, (phone,))
                
                row = cursor.fetchone()
                return dict(row) if row else None
                
        except Exception as e:
            self.logger.error(f"Error fetching customer by phone: {e}")
            return None
            
    def search_customers(self, query: str) -> List[Dict[str, Any]]:
        """Search customers by name, phone, or national ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM customers
                    WHERE is_active = 1 AND (
                        name LIKE ? OR phone LIKE ? OR national_id LIKE ?
                    )
                    ORDER BY name
                    LIMIT 50
                """, (f"%{query}%", f"%{query}%", f"%{query}%"))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Error searching customers: {e}")
            return []
            
    def get_customer_purchase_history(self, customer_id: int, limit: int = 20) -> List[Dict[str, Any]]:
        """Get customer's purchase history"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT i.*, u.full_name as cashier_name
                    FROM invoices i
                    LEFT JOIN users u ON i.user_id = u.id
                    WHERE i.customer_id = ? AND i.status = 'completed'
                    ORDER BY i.created_at DESC
                    LIMIT ?
                """, (customer_id, limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Error fetching customer purchase history: {e}")
            return []
            
    def get_customer_statistics(self, customer_id: int) -> Dict[str, Any]:
        """Get customer statistics"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Total purchases
                cursor.execute("""
                    SELECT COUNT(*) as total_orders, 
                           COALESCE(SUM(total_amount), 0) as total_spent,
                           COALESCE(AVG(total_amount), 0) as avg_order_value
                    FROM invoices 
                    WHERE customer_id = ? AND status = 'completed'
                """, (customer_id,))
                
                stats = dict(cursor.fetchone())
                
                # Last purchase date
                cursor.execute("""
                    SELECT MAX(created_at) as last_purchase_date
                    FROM invoices 
                    WHERE customer_id = ? AND status = 'completed'
                """, (customer_id,))
                
                last_purchase = cursor.fetchone()
                stats['last_purchase_date'] = last_purchase['last_purchase_date'] if last_purchase else None
                
                return stats
                
        except Exception as e:
            self.logger.error(f"Error fetching customer statistics: {e}")
            return {}
            
    def update_customer_balance(self, customer_id: int, amount: float, operation: str = 'add') -> bool:
        """Update customer balance (for credit customers)"""
        try:
            if not auth_manager.can_access_feature('customers'):
                return False
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get current balance
                cursor.execute("SELECT current_balance FROM customers WHERE id = ?", (customer_id,))
                row = cursor.fetchone()
                
                if not row:
                    return False
                    
                current_balance = row['current_balance']
                
                if operation == 'add':
                    new_balance = current_balance + amount
                elif operation == 'subtract':
                    new_balance = current_balance - amount
                else:
                    new_balance = amount  # set operation
                    
                cursor.execute("""
                    UPDATE customers SET current_balance = ? WHERE id = ?
                """, (new_balance, customer_id))
                
                conn.commit()
                
                self.logger.info(f"Customer balance updated: ID {customer_id}, {current_balance} -> {new_balance}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating customer balance: {e}")
            return False
            
    def get_customers_with_credit(self) -> List[Dict[str, Any]]:
        """Get customers with outstanding credit balance"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM customers
                    WHERE is_active = 1 AND current_balance > 0
                    ORDER BY current_balance DESC
                """)
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Error fetching customers with credit: {e}")
            return []
