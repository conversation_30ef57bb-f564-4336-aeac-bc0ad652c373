#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qatar POS System - نظام نقاط البيع القطري
Main application entry point

Author: Development Team
Date: 2025-06-19
License: MIT
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import core modules
from config.settings import Settings
from database.database_manager import DatabaseManager
from ui.login_window import LoginWindow
from utils.logger import setup_logger

def show_startup_info():
    """Show startup information"""
    print("🇶🇦 Qatar POS System - نظام نقاط البيع القطري")
    print("=" * 50)
    print("🚀 Starting application...")
    print("📍 Designed for Qatar retail market")
    print("🌐 Arabic/English bilingual support")
    print("💰 QAR currency support")
    print("📱 Modern POS functionality")
    print("=" * 50)

def create_directories():
    """Create necessary directories"""
    directories = [
        'logs',
        'database',
        'assets',
        'assets/products',
        'assets/fonts',
        'invoices',
        'receipts',
        'database/backups'
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

def main():
    """Main application entry point"""
    try:
        # Show startup info
        show_startup_info()

        # Create necessary directories
        create_directories()

        # Setup logging
        logger = setup_logger()
        logger.info("Starting Qatar POS System...")

        # Load configuration
        settings = Settings()
        logger.info(f"Loaded configuration for language: {settings.get('language', 'APP', 'ar')}")

        # Initialize database
        print("🗄️  Initializing database...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        logger.info("Database initialized successfully")

        # Start the application
        print("🔐 Starting login interface...")
        app = LoginWindow()
        app.run()

        logger.info("Qatar POS System shutdown complete")
        print("👋 Thank you for using Qatar POS System!")

    except KeyboardInterrupt:
        print("\n⚠️  Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"💥 Fatal error starting application: {e}")
        logging.error(f"Application startup error: {e}", exc_info=True)

        # Try to show error in GUI if possible
        try:
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ فادح - Fatal Error",
                               f"فشل في تشغيل النظام\nFailed to start system:\n\n{str(e)}")
        except:
            pass

        sys.exit(1)

if __name__ == "__main__":
    main()
