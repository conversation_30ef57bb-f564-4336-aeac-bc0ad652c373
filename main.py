#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qatar POS System - نظام نقاط البيع القطري
Main application entry point

Author: Development Team
Date: 2025-06-19
License: MIT
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import core modules
from config.settings import Settings
from database.database_manager import DatabaseManager
from ui.login_window import LoginWindow
from utils.logger import setup_logger

def main():
    """Main application entry point"""
    try:
        # Setup logging
        logger = setup_logger()
        logger.info("Starting Qatar POS System...")
        
        # Load configuration
        settings = Settings()
        logger.info(f"Loaded configuration for language: {settings.get('language', 'en')}")
        
        # Initialize database
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        logger.info("Database initialized successfully")
        
        # Start the application
        app = LoginWindow()
        app.run()
        
    except Exception as e:
        print(f"Error starting application: {e}")
        logging.error(f"Application startup error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
