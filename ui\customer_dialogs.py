#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Customer management dialogs for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import re

from config.settings import Settings
from utils.logger import get_logger

class CustomerDialog:
    """Customer creation/editing dialog"""
    
    def __init__(self, parent, title, customer_data=None):
        self.parent = parent
        self.customer_data = customer_data or {}
        self.settings = Settings()
        self.logger = get_logger()
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.setup_ui()
        self.load_data()
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
    def setup_ui(self):
        """Setup dialog UI"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Personal info section
        personal_frame = ttk.LabelFrame(main_frame, text="المعلومات الشخصية - Personal Information", padding="10")
        personal_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Name
        ttk.Label(personal_frame, text="الاسم - Name: *").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(personal_frame, textvariable=self.name_var, width=40).grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Phone
        ttk.Label(personal_frame, text="رقم الهاتف - Phone:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.phone_var = tk.StringVar()
        phone_entry = ttk.Entry(personal_frame, textvariable=self.phone_var, width=40)
        phone_entry.grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        phone_entry.bind('<KeyRelease>', self.validate_phone)
        
        # Email
        ttk.Label(personal_frame, text="البريد الإلكتروني - Email:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar()
        email_entry = ttk.Entry(personal_frame, textvariable=self.email_var, width=40)
        email_entry.grid(row=2, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        email_entry.bind('<KeyRelease>', self.validate_email)
        
        # National ID
        ttk.Label(personal_frame, text="رقم الهوية - National ID:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.national_id_var = tk.StringVar()
        national_id_entry = ttk.Entry(personal_frame, textvariable=self.national_id_var, width=40)
        national_id_entry.grid(row=3, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        national_id_entry.bind('<KeyRelease>', self.validate_national_id)
        
        personal_frame.grid_columnconfigure(1, weight=1)
        
        # Address section
        address_frame = ttk.LabelFrame(main_frame, text="العنوان - Address", padding="10")
        address_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(address_frame, text="العنوان - Address:").pack(anchor=tk.W, pady=(0, 5))
        self.address_text = tk.Text(address_frame, height=3, width=40)
        self.address_text.pack(fill=tk.X)
        
        # Credit section
        credit_frame = ttk.LabelFrame(main_frame, text="الائتمان - Credit", padding="10")
        credit_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Credit limit
        ttk.Label(credit_frame, text="حد الائتمان - Credit Limit:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.credit_limit_var = tk.StringVar(value="0.00")
        credit_frame_inner = ttk.Frame(credit_frame)
        credit_frame_inner.grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        ttk.Entry(credit_frame_inner, textvariable=self.credit_limit_var, width=15).pack(side=tk.LEFT)
        ttk.Label(credit_frame_inner, text="QAR").pack(side=tk.LEFT, padx=(5, 0))
        
        credit_frame.grid_columnconfigure(1, weight=1)
        
        # Validation labels
        self.validation_frame = ttk.Frame(main_frame)
        self.validation_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.phone_validation_label = ttk.Label(self.validation_frame, text="", foreground="red")
        self.phone_validation_label.pack(anchor=tk.W)
        
        self.email_validation_label = ttk.Label(self.validation_frame, text="", foreground="red")
        self.email_validation_label.pack(anchor=tk.W)
        
        self.national_id_validation_label = ttk.Label(self.validation_frame, text="", foreground="red")
        self.national_id_validation_label.pack(anchor=tk.W)
        
        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="حفظ - Save", command=self.save_customer).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء - Cancel", command=self.cancel).pack(side=tk.RIGHT)
        
    def validate_phone(self, event=None):
        """Validate phone number format"""
        phone = self.phone_var.get()
        if phone:
            # Qatar phone number pattern: +974 XXXX XXXX or similar
            if not re.match(r'^(\+974\s?)?[0-9\s-]{8,12}$', phone):
                self.phone_validation_label.config(text="تنسيق رقم الهاتف غير صحيح - Invalid phone format")
            else:
                self.phone_validation_label.config(text="")
        else:
            self.phone_validation_label.config(text="")
            
    def validate_email(self, event=None):
        """Validate email format"""
        email = self.email_var.get()
        if email:
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                self.email_validation_label.config(text="تنسيق البريد الإلكتروني غير صحيح - Invalid email format")
            else:
                self.email_validation_label.config(text="")
        else:
            self.email_validation_label.config(text="")
            
    def validate_national_id(self, event=None):
        """Validate national ID format"""
        national_id = self.national_id_var.get()
        if national_id:
            # Qatar national ID is typically 11 digits
            if not re.match(r'^[0-9]{11}$', national_id):
                self.national_id_validation_label.config(text="رقم الهوية يجب أن يكون 11 رقم - National ID should be 11 digits")
            else:
                self.national_id_validation_label.config(text="")
        else:
            self.national_id_validation_label.config(text="")
            
    def load_data(self):
        """Load existing customer data if editing"""
        if not self.customer_data:
            return
            
        self.name_var.set(self.customer_data.get('name', ''))
        self.phone_var.set(self.customer_data.get('phone', ''))
        self.email_var.set(self.customer_data.get('email', ''))
        self.national_id_var.set(self.customer_data.get('national_id', ''))
        self.credit_limit_var.set(str(self.customer_data.get('credit_limit', 0.0)))
        
        address = self.customer_data.get('address', '')
        self.address_text.delete(1.0, tk.END)
        self.address_text.insert(1.0, address)
        
    def save_customer(self):
        """Save customer data"""
        try:
            # Validate required fields
            if not self.name_var.get().strip():
                messagebox.showerror("خطأ - Error", "يرجى إدخال الاسم\nPlease enter name")
                return
                
            # Check validation errors
            if (self.phone_validation_label.cget("text") or 
                self.email_validation_label.cget("text") or 
                self.national_id_validation_label.cget("text")):
                messagebox.showerror("خطأ - Error", "يرجى تصحيح الأخطاء أولاً\nPlease fix validation errors first")
                return
                
            # Validate credit limit
            try:
                credit_limit = float(self.credit_limit_var.get())
                if credit_limit < 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("خطأ - Error", "يرجى إدخال حد ائتمان صحيح\nPlease enter valid credit limit")
                return
                
            # Prepare result data
            self.result = {
                'name': self.name_var.get().strip(),
                'phone': self.phone_var.get().strip() or None,
                'email': self.email_var.get().strip() or None,
                'national_id': self.national_id_var.get().strip() or None,
                'address': self.address_text.get(1.0, tk.END).strip() or None,
                'credit_limit': credit_limit
            }
            
            self.dialog.destroy()
            
        except Exception as e:
            self.logger.error(f"Error saving customer: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في حفظ البيانات\nError saving data: {str(e)}")
            
    def cancel(self):
        """Cancel dialog"""
        self.result = None
        self.dialog.destroy()

class BalanceUpdateDialog:
    """Customer balance update dialog"""
    
    def __init__(self, parent, customer_id):
        self.parent = parent
        self.customer_id = customer_id
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("تحديث الرصيد - Update Balance")
        self.dialog.geometry("400x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.setup_ui()
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
    def setup_ui(self):
        """Setup dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Operation type
        ttk.Label(main_frame, text="نوع العملية - Operation Type:").pack(anchor=tk.W, pady=(0, 5))
        self.operation_var = tk.StringVar(value="add")
        
        operation_frame = ttk.Frame(main_frame)
        operation_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Radiobutton(operation_frame, text="إضافة - Add", 
                       variable=self.operation_var, value="add").pack(side=tk.LEFT)
        ttk.Radiobutton(operation_frame, text="خصم - Subtract", 
                       variable=self.operation_var, value="subtract").pack(side=tk.LEFT, padx=(20, 0))
        ttk.Radiobutton(operation_frame, text="تعيين - Set", 
                       variable=self.operation_var, value="set").pack(side=tk.LEFT, padx=(20, 0))
        
        # Amount
        ttk.Label(main_frame, text="المبلغ - Amount:").pack(anchor=tk.W, pady=(0, 5))
        amount_frame = ttk.Frame(main_frame)
        amount_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.amount_var = tk.StringVar()
        ttk.Entry(amount_frame, textvariable=self.amount_var, width=15).pack(side=tk.LEFT)
        ttk.Label(amount_frame, text="QAR").pack(side=tk.LEFT, padx=(5, 0))
        
        # Notes
        ttk.Label(main_frame, text="ملاحظات - Notes:").pack(anchor=tk.W, pady=(0, 5))
        self.notes_text = tk.Text(main_frame, height=3, width=40)
        self.notes_text.pack(fill=tk.X, pady=(0, 15))
        
        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="تحديث - Update", command=self.update_balance).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء - Cancel", command=self.cancel).pack(side=tk.RIGHT)
        
    def update_balance(self):
        """Update balance"""
        try:
            amount = float(self.amount_var.get())
            if amount < 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ - Error", "يرجى إدخال مبلغ صحيح\nPlease enter valid amount")
            return
            
        operation = self.operation_var.get()
        
        self.result = {
            'amount': amount,
            'operation': operation,
            'notes': self.notes_text.get(1.0, tk.END).strip()
        }
        
        self.dialog.destroy()
        
    def cancel(self):
        """Cancel dialog"""
        self.result = None
        self.dialog.destroy()
