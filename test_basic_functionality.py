#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Basic functionality test for Qatar POS System
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_functionality():
    """Test basic system functionality"""
    print("🧪 Testing Qatar POS System Basic Functionality...")
    
    try:
        # Test 1: Configuration
        print("\n1. Testing Configuration...")
        from config.settings import Settings
        settings = Settings()
        print(f"   ✅ Language: {settings.get('language')}")
        print(f"   ✅ Currency: {settings.get('currency', 'BUSINESS')}")
        
        # Test 2: Database initialization
        print("\n2. Testing Database...")
        from database.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("   ✅ Database initialized successfully")
        
        # Test 3: Authentication
        print("\n3. Testing Authentication...")
        from core.auth import auth_manager
        
        # Test login
        if auth_manager.authenticate("admin", "admin123"):
            print("   ✅ Admin login successful")
            user = auth_manager.get_current_user()
            print(f"   ✅ Current user: {user.full_name} ({user.role.value})")
            
            # Test permissions
            if auth_manager.can_access_feature('sales'):
                print("   ✅ Sales permission granted")
            if auth_manager.can_access_feature('user_management'):
                print("   ✅ User management permission granted")
        else:
            print("   ❌ Admin login failed")
            return False
            
        # Test 4: Product Management
        print("\n4. Testing Product Management...")
        from core.product_manager import ProductManager, CategoryManager
        from database.models import Product
        
        category_manager = CategoryManager()
        categories = category_manager.get_all_categories()
        print(f"   ✅ Found {len(categories)} categories")
        
        product_manager = ProductManager()
        
        # Create a test product
        test_product = Product(
            barcode="1234567890123",
            name_ar="منتج تجريبي",
            name_en="Test Product",
            description="Test product for system verification",
            category_id=categories[0]['id'] if categories else None,
            cost_price=10.0,
            selling_price=15.0,
            stock_quantity=100,
            min_stock_level=10,
            unit="piece"
        )
        
        if product_manager.create_product(test_product):
            print("   ✅ Test product created successfully")
            
            # Search for the product
            search_results = product_manager.search_products("Test")
            if search_results:
                print(f"   ✅ Product search working: found {len(search_results)} results")
            else:
                print("   ⚠️  Product search returned no results")
        else:
            print("   ❌ Failed to create test product")
            
        # Test 5: Customer Management
        print("\n5. Testing Customer Management...")
        from core.customer_manager import CustomerManager
        from database.models import Customer
        
        customer_manager = CustomerManager()
        
        test_customer = Customer(
            name="عميل تجريبي - Test Customer",
            phone="+974 5555 1234",
            email="<EMAIL>",
            national_id="12345678901",
            address="Doha, Qatar",
            credit_limit=1000.0
        )
        
        customer_id = customer_manager.create_customer(test_customer)
        if customer_id:
            print(f"   ✅ Test customer created successfully (ID: {customer_id})")
        else:
            print("   ❌ Failed to create test customer")
            
        # Test 6: Invoice Management
        print("\n6. Testing Invoice Management...")
        from core.invoice_manager import InvoiceManager
        from database.models import PaymentMethod
        
        invoice_manager = InvoiceManager()
        
        # Create test invoice
        invoice_id = invoice_manager.create_invoice(customer_id)
        if invoice_id:
            print(f"   ✅ Test invoice created successfully (ID: {invoice_id})")
            
            # Add item to invoice
            product = product_manager.search_products("Test")[0] if product_manager.search_products("Test") else None
            if product:
                if invoice_manager.add_item_to_invoice(invoice_id, product['id'], 2, product['selling_price']):
                    print("   ✅ Item added to invoice successfully")
                    
                    # Complete the invoice
                    if invoice_manager.complete_invoice(invoice_id, PaymentMethod.CASH):
                        print("   ✅ Invoice completed successfully")
                        
                        # Verify stock was updated
                        updated_product = product_manager.get_product_by_id(product['id'])
                        if updated_product and updated_product['stock_quantity'] == 98:  # 100 - 2
                            print("   ✅ Stock quantity updated correctly")
                        else:
                            print("   ⚠️  Stock quantity not updated correctly")
                    else:
                        print("   ❌ Failed to complete invoice")
                else:
                    print("   ❌ Failed to add item to invoice")
            else:
                print("   ⚠️  No test product found for invoice")
        else:
            print("   ❌ Failed to create test invoice")
            
        print("\n🎉 Basic functionality test completed!")
        print("✅ All core components are working correctly")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    sys.exit(0 if success else 1)
