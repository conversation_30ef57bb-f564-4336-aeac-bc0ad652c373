#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logging utility for Qatar POS System
"""

import logging
import os
from pathlib import Path
from datetime import datetime

def setup_logger(name: str = 'qatar_pos', level: int = logging.INFO) -> logging.Logger:
    """Setup application logger with file and console handlers"""

    # Create logs directory
    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)

    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Avoid duplicate handlers
    if logger.handlers:
        return logger

    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    console_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )

    # File handler - daily rotation
    today = datetime.now().strftime('%Y-%m-%d')
    log_file = log_dir / f'pos_system_{today}.log'

    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def get_logger(name: str = 'qatar_pos') -> logging.Logger:
    """Get existing logger instance"""
    return logging.getLogger(name)
