#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick system test for Qatar POS System
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test if all modules can be imported"""
    print("🧪 Testing module imports...")
    
    modules_to_test = [
        ('config.settings', 'Settings'),
        ('database.database_manager', 'DatabaseManager'),
        ('database.models', 'User'),
        ('core.auth', 'AuthManager'),
        ('core.product_manager', 'ProductManager'),
        ('core.customer_manager', 'CustomerManager'),
        ('core.invoice_manager', 'InvoiceManager'),
        ('core.reports_manager', 'ReportsManager'),
        ('core.hardware_manager', 'HardwareManager'),
        ('ui.login_window', 'LoginWindow'),
        ('ui.main_window', 'MainWindow'),
        ('utils.logger', 'setup_logger')
    ]
    
    failed_imports = []
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"   ✅ {module_name}.{class_name}")
        except Exception as e:
            print(f"   ❌ {module_name}.{class_name} - {e}")
            failed_imports.append((module_name, class_name, str(e)))
    
    return len(failed_imports) == 0, failed_imports

def test_database():
    """Test database initialization"""
    print("\n🗄️  Testing database...")
    
    try:
        from database.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("   ✅ Database initialization successful")
        return True
    except Exception as e:
        print(f"   ❌ Database initialization failed: {e}")
        return False

def test_authentication():
    """Test authentication system"""
    print("\n🔐 Testing authentication...")
    
    try:
        from core.auth import auth_manager
        
        # Test login
        if auth_manager.authenticate("admin", "admin123"):
            print("   ✅ Admin login successful")
            
            user = auth_manager.get_current_user()
            if user:
                print(f"   ✅ Current user: {user.full_name}")
                
                # Test permissions
                if auth_manager.can_access_feature('sales'):
                    print("   ✅ Sales permission check")
                else:
                    print("   ❌ Sales permission check failed")
                    
                auth_manager.logout()
                print("   ✅ Logout successful")
                return True
            else:
                print("   ❌ Failed to get current user")
                return False
        else:
            print("   ❌ Admin login failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Authentication test failed: {e}")
        return False

def test_core_functionality():
    """Test core business functionality"""
    print("\n⚙️  Testing core functionality...")
    
    try:
        from core.product_manager import ProductManager, CategoryManager
        from core.customer_manager import CustomerManager
        from core.invoice_manager import InvoiceManager
        
        # Test managers initialization
        product_manager = ProductManager()
        category_manager = CategoryManager()
        customer_manager = CustomerManager()
        invoice_manager = InvoiceManager()
        
        print("   ✅ All managers initialized successfully")
        
        # Test basic operations
        categories = category_manager.get_all_categories()
        print(f"   ✅ Found {len(categories)} categories")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Core functionality test failed: {e}")
        return False

def test_ui_components():
    """Test UI components"""
    print("\n🖥️  Testing UI components...")
    
    try:
        # Test if tkinter is available
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        root.destroy()
        print("   ✅ Tkinter available")
        
        # Test UI imports
        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        print("   ✅ UI components importable")
        
        return True
        
    except Exception as e:
        print(f"   ❌ UI components test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🇶🇦 Qatar POS System - System Test")
    print("=" * 40)
    
    all_tests_passed = True
    
    # Test imports
    imports_ok, failed_imports = test_imports()
    if not imports_ok:
        all_tests_passed = False
        print(f"\n❌ {len(failed_imports)} import(s) failed:")
        for module, class_name, error in failed_imports:
            print(f"   - {module}.{class_name}: {error}")
    
    # Test database
    if not test_database():
        all_tests_passed = False
    
    # Test authentication
    if not test_authentication():
        all_tests_passed = False
    
    # Test core functionality
    if not test_core_functionality():
        all_tests_passed = False
    
    # Test UI components
    if not test_ui_components():
        all_tests_passed = False
    
    # Summary
    print("\n" + "=" * 40)
    if all_tests_passed:
        print("🎉 All tests passed! System is ready to run.")
        print("\n🚀 To start the system, run:")
        print("   python main.py")
        print("\n   or use the launcher:")
        print("   Windows: run_pos.bat")
        print("   Linux/Mac: ./run_pos.sh")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\n🔧 Common solutions:")
        print("   - Install missing packages: pip install -r requirements.txt")
        print("   - Check Python version (3.8+ required)")
        print("   - Ensure all files are present")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
