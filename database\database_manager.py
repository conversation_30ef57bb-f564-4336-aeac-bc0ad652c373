#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database manager for Qatar POS System
"""

import sqlite3
import os
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from contextlib import contextmanager

from .models import User, UserRole, Product, Customer, Invoice, InvoiceItem, Category, StockMovement, SystemSetting
from utils.logger import get_logger

class DatabaseManager:
    """Database operations manager"""
    
    def __init__(self, db_path: str = "database/pos_system.db"):
        self.db_path = Path(db_path)
        self.logger = get_logger()
        
        # Ensure database directory exists
        self.db_path.parent.mkdir(exist_ok=True)
        
    @contextmanager
    def get_connection(self):
        """Get database connection with context manager"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()
            
    def initialize_database(self):
        """Initialize database with tables and default data"""
        self.logger.info("Initializing database...")
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Create tables
            self._create_tables(cursor)
            
            # Insert default data
            self._insert_default_data(cursor)
            
            conn.commit()
            
        self.logger.info("Database initialized successfully")
        
    def _create_tables(self, cursor: sqlite3.Cursor):
        """Create all database tables"""
        
        # Users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        """)
        
        # Categories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name_ar TEXT NOT NULL,
                name_en TEXT NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Products table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                barcode TEXT UNIQUE,
                name_ar TEXT NOT NULL,
                name_en TEXT NOT NULL,
                description TEXT,
                category_id INTEGER,
                cost_price REAL DEFAULT 0.0,
                selling_price REAL DEFAULT 0.0,
                stock_quantity INTEGER DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                unit TEXT DEFAULT 'piece',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        """)
        
        # Customers table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                national_id TEXT,
                address TEXT,
                credit_limit REAL DEFAULT 0.0,
                current_balance REAL DEFAULT 0.0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Invoices table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                user_id INTEGER NOT NULL,
                subtotal REAL DEFAULT 0.0,
                tax_amount REAL DEFAULT 0.0,
                discount_amount REAL DEFAULT 0.0,
                total_amount REAL DEFAULT 0.0,
                payment_method TEXT DEFAULT 'cash',
                status TEXT DEFAULT 'pending',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # Invoice items table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                discount_amount REAL DEFAULT 0.0,
                total_amount REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """)
        
        # Stock movements table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL,
                quantity REAL NOT NULL,
                reference_type TEXT,
                reference_id INTEGER,
                notes TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # System settings table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT NOT NULL,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_products_barcode ON products (barcode)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices (invoice_number)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices (created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers (phone)")
        
    def _insert_default_data(self, cursor: sqlite3.Cursor):
        """Insert default data"""
        
        # Check if admin user exists
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        if cursor.fetchone()[0] == 0:
            # Create default admin user
            admin_password = self._hash_password("admin123")
            cursor.execute("""
                INSERT INTO users (username, password_hash, full_name, role, email)
                VALUES (?, ?, ?, ?, ?)
            """, ("admin", admin_password, "System Administrator", "manager", "<EMAIL>"))
            
        # Insert default categories
        default_categories = [
            ("طعام ومشروبات", "Food & Beverages", "Food and beverage products"),
            ("إلكترونيات", "Electronics", "Electronic devices and accessories"),
            ("ملابس", "Clothing", "Clothing and fashion items"),
            ("منزل وحديقة", "Home & Garden", "Home and garden products"),
            ("صحة وجمال", "Health & Beauty", "Health and beauty products")
        ]
        
        for name_ar, name_en, description in default_categories:
            cursor.execute("""
                INSERT OR IGNORE INTO categories (name_ar, name_en, description)
                VALUES (?, ?, ?)
            """, (name_ar, name_en, description))
            
        # Insert default system settings
        default_settings = [
            ("company_name_ar", "شركة قطر للتجارة", "Company name in Arabic"),
            ("company_name_en", "Qatar Trading Company", "Company name in English"),
            ("vat_rate", "0.0", "VAT rate percentage"),
            ("currency_code", "QAR", "Currency code"),
            ("currency_symbol", "ر.ق", "Currency symbol"),
            ("invoice_prefix", "INV", "Invoice number prefix"),
            ("next_invoice_number", "1", "Next invoice number")
        ]
        
        for key, value, description in default_settings:
            cursor.execute("""
                INSERT OR IGNORE INTO system_settings (key, value, description)
                VALUES (?, ?, ?)
            """, (key, value, description))
            
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
