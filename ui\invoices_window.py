#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Invoices management window for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import subprocess
import os
from pathlib import Path

from core.invoice_manager import InvoiceManager
from core.invoice_printer import InvoicePrinter
from core.auth import auth_manager
from config.settings import Settings
from utils.logger import get_logger

class InvoicesWindow:
    """Invoices management window"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.invoice_manager = InvoiceManager()
        self.invoice_printer = InvoicePrinter()
        self.settings = Settings()
        self.logger = get_logger()
        
        self.window = None
        self.selected_invoice_id = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the invoices management interface"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("إدارة الفواتير - Invoices Management")
        self.window.geometry("1200x700")
        
        # Create main layout
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
        
        # Load initial data
        self.refresh_invoices()
        
    def create_toolbar(self):
        """Create toolbar with action buttons"""
        toolbar = ttk.Frame(self.window)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # Invoice actions
        ttk.Button(toolbar, text="عرض - View", 
                  command=self.view_invoice).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="طباعة PDF - Print PDF", 
                  command=self.print_pdf).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="طباعة حرارية - Thermal Print", 
                  command=self.print_thermal).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Export actions
        ttk.Button(toolbar, text="تصدير - Export", 
                  command=self.export_invoices).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="إرسال بالبريد - Email", 
                  command=self.email_invoice).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Date filter
        ttk.Label(toolbar, text="من - From:").pack(side=tk.LEFT, padx=(10, 5))
        self.from_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        from_date_entry = ttk.Entry(toolbar, textvariable=self.from_date_var, width=12)
        from_date_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(toolbar, text="إلى - To:").pack(side=tk.LEFT, padx=(5, 5))
        self.to_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        to_date_entry = ttk.Entry(toolbar, textvariable=self.to_date_var, width=12)
        to_date_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        # Refresh button
        ttk.Button(toolbar, text="تحديث - Refresh", 
                  command=self.refresh_invoices).pack(side=tk.RIGHT)
        
    def create_main_content(self):
        """Create main content area"""
        # Create paned window for resizable layout
        paned = ttk.PanedWindow(self.window, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Invoices list
        left_frame = ttk.Frame(paned)
        paned.add(left_frame, weight=2)
        
        self.create_invoices_list(left_frame)
        
        # Right panel - Invoice details
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)
        
        self.create_invoice_details(right_frame)
        
    def create_invoices_list(self, parent):
        """Create invoices list with search and filters"""
        # Search and filter section
        search_frame = ttk.LabelFrame(parent, text="البحث والتصفية - Search & Filter", padding="10")
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Search entry
        search_row1 = ttk.Frame(search_frame)
        search_row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(search_row1, text="البحث - Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_row1, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 5))
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        ttk.Button(search_row1, text="بحث - Search", 
                  command=self.search_invoices).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_row1, text="مسح - Clear", 
                  command=self.clear_search).pack(side=tk.LEFT)
        
        # Status filter
        search_row2 = ttk.Frame(search_frame)
        search_row2.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(search_row2, text="الحالة - Status:").pack(side=tk.LEFT)
        self.status_var = tk.StringVar(value="all")
        
        status_filters = [
            ("الكل - All", "all"),
            ("مكتملة - Completed", "completed"),
            ("معلقة - Pending", "pending"),
            ("ملغية - Cancelled", "cancelled")
        ]
        
        for text, value in status_filters:
            ttk.Radiobutton(search_row2, text=text, variable=self.status_var, 
                          value=value, command=self.refresh_invoices).pack(side=tk.LEFT, padx=(10, 0))
        
        # Invoices list
        list_frame = ttk.LabelFrame(parent, text="قائمة الفواتير - Invoices List", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview for invoices
        columns = ('invoice_number', 'date', 'customer', 'total', 'payment_method', 'status')
        self.invoices_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.invoices_tree.heading('invoice_number', text='رقم الفاتورة - Invoice No.')
        self.invoices_tree.heading('date', text='التاريخ - Date')
        self.invoices_tree.heading('customer', text='العميل - Customer')
        self.invoices_tree.heading('total', text='المجموع - Total')
        self.invoices_tree.heading('payment_method', text='طريقة الدفع - Payment')
        self.invoices_tree.heading('status', text='الحالة - Status')
        
        self.invoices_tree.column('invoice_number', width=120)
        self.invoices_tree.column('date', width=100)
        self.invoices_tree.column('customer', width=150)
        self.invoices_tree.column('total', width=100, anchor=tk.CENTER)
        self.invoices_tree.column('payment_method', width=100, anchor=tk.CENTER)
        self.invoices_tree.column('status', width=80, anchor=tk.CENTER)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.invoices_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.invoices_tree.xview)
        self.invoices_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.invoices_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # Bind selection event
        self.invoices_tree.bind('<<TreeviewSelect>>', self.on_invoice_select)
        self.invoices_tree.bind('<Double-1>', self.view_invoice)
        
    def create_invoice_details(self, parent):
        """Create invoice details panel"""
        details_frame = ttk.LabelFrame(parent, text="تفاصيل الفاتورة - Invoice Details", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # Invoice info
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Create info labels
        self.info_labels = {}
        info_fields = [
            ('invoice_number', 'رقم الفاتورة - Invoice No.'),
            ('date', 'التاريخ - Date'),
            ('customer', 'العميل - Customer'),
            ('cashier', 'الكاشير - Cashier'),
            ('payment_method', 'طريقة الدفع - Payment Method'),
            ('status', 'الحالة - Status'),
            ('subtotal', 'المجموع الفرعي - Subtotal'),
            ('discount', 'الخصم - Discount'),
            ('tax', 'الضريبة - Tax'),
            ('total', 'المجموع الكلي - Total')
        ]
        
        for i, (field, label) in enumerate(info_fields):
            ttk.Label(info_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.info_labels[field] = ttk.Label(info_frame, text="-", relief=tk.SUNKEN, width=25)
            self.info_labels[field].grid(row=i, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=2)
        
        info_frame.grid_columnconfigure(1, weight=1)
        
        # Items list
        items_frame = ttk.LabelFrame(details_frame, text="الأصناف - Items", padding="5")
        items_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Items treeview
        items_columns = ('item', 'qty', 'price', 'total')
        self.items_tree = ttk.Treeview(items_frame, columns=items_columns, 
                                     show='headings', height=10)
        
        self.items_tree.heading('item', text='الصنف - Item')
        self.items_tree.heading('qty', text='الكمية - Qty')
        self.items_tree.heading('price', text='السعر - Price')
        self.items_tree.heading('total', text='المجموع - Total')
        
        self.items_tree.column('item', width=150)
        self.items_tree.column('qty', width=60, anchor=tk.CENTER)
        self.items_tree.column('price', width=80, anchor=tk.CENTER)
        self.items_tree.column('total', width=80, anchor=tk.CENTER)
        
        items_scrollbar = ttk.Scrollbar(items_frame, orient=tk.VERTICAL, 
                                      command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        items_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_status_bar(self):
        """Create status bar"""
        status_frame = ttk.Frame(self.window)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar(value="جاهز - Ready")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # Invoices count and total
        self.invoices_count_var = tk.StringVar(value="الفواتير: 0")
        ttk.Label(status_frame, textvariable=self.invoices_count_var).pack(side=tk.RIGHT, padx=5)
        
        self.total_amount_var = tk.StringVar(value="المجموع: 0.00 QAR")
        ttk.Label(status_frame, textvariable=self.total_amount_var).pack(side=tk.RIGHT, padx=5)

    def refresh_invoices(self):
        """Refresh invoices list"""
        try:
            # Clear existing items
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)

            # Get filter criteria
            search_query = self.search_var.get().strip()
            status_filter = self.status_var.get()
            from_date = self.from_date_var.get()
            to_date = self.to_date_var.get()

            # Get invoices
            invoices = self.get_filtered_invoices(search_query, status_filter, from_date, to_date)

            total_amount = 0.0

            # Populate treeview
            for invoice in invoices:
                # Format date
                date_str = invoice['created_at'][:10] if invoice['created_at'] else '-'

                # Get customer name
                customer_name = invoice.get('customer_name', 'نقدي / Cash')

                # Format total
                invoice_total = invoice.get('total_amount', 0)
                total_amount += invoice_total

                values = (
                    invoice['invoice_number'],
                    date_str,
                    customer_name,
                    f"{invoice_total:.2f}",
                    invoice.get('payment_method', ''),
                    invoice.get('status', '')
                )

                # Color coding based on status
                tags = []
                if invoice.get('status') == 'completed':
                    tags = ['completed']
                elif invoice.get('status') == 'pending':
                    tags = ['pending']
                elif invoice.get('status') == 'cancelled':
                    tags = ['cancelled']

                item = self.invoices_tree.insert('', tk.END, values=values, tags=tags)
                # Store invoice ID in item
                self.invoices_tree.set(item, '#0', invoice['id'])

            # Configure tag colors
            self.invoices_tree.tag_configure('completed', background='#d4edda')
            self.invoices_tree.tag_configure('pending', background='#fff3cd')
            self.invoices_tree.tag_configure('cancelled', background='#f8d7da')

            # Update status
            self.invoices_count_var.set(f"الفواتير: {len(invoices)}")
            self.total_amount_var.set(f"المجموع: {total_amount:.2f} QAR")
            self.status_var.set("تم تحديث قائمة الفواتير - Invoices list updated")

        except Exception as e:
            self.logger.error(f"Error refreshing invoices: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في تحديث الفواتير\nError refreshing invoices: {str(e)}")

    def get_filtered_invoices(self, search_query, status_filter, from_date, to_date):
        """Get filtered invoices"""
        try:
            with self.invoice_manager.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                sql = """
                    SELECT i.*, c.name as customer_name, u.full_name as cashier_name
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    LEFT JOIN users u ON i.user_id = u.id
                    WHERE 1=1
                """
                params = []

                # Date filter
                if from_date:
                    sql += " AND DATE(i.created_at) >= ?"
                    params.append(from_date)
                if to_date:
                    sql += " AND DATE(i.created_at) <= ?"
                    params.append(to_date)

                # Status filter
                if status_filter != "all":
                    sql += " AND i.status = ?"
                    params.append(status_filter)

                # Search filter
                if search_query:
                    sql += " AND (i.invoice_number LIKE ? OR c.name LIKE ? OR u.full_name LIKE ?)"
                    search_param = f"%{search_query}%"
                    params.extend([search_param, search_param, search_param])

                sql += " ORDER BY i.created_at DESC"

                cursor.execute(sql, params)
                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"Error getting filtered invoices: {e}")
            return []

    def on_search_change(self, event=None):
        """Handle search text change"""
        # Auto-search after a short delay
        if hasattr(self, '_search_timer'):
            self.window.after_cancel(self._search_timer)
        self._search_timer = self.window.after(500, self.search_invoices)

    def search_invoices(self):
        """Search invoices"""
        self.refresh_invoices()

    def clear_search(self):
        """Clear search"""
        self.search_var.set("")
        self.refresh_invoices()

    def on_invoice_select(self, event=None):
        """Handle invoice selection"""
        selection = self.invoices_tree.selection()
        if not selection:
            self.clear_invoice_details()
            return

        item = selection[0]
        invoice_id = self.invoices_tree.set(item, '#0')

        try:
            invoice_id = int(invoice_id)
            self.selected_invoice_id = invoice_id
            self.load_invoice_details(invoice_id)

        except (ValueError, TypeError):
            self.clear_invoice_details()

    def load_invoice_details(self, invoice_id):
        """Load invoice details"""
        try:
            invoice = self.invoice_manager.get_invoice_by_id(invoice_id)
            if not invoice:
                self.clear_invoice_details()
                return

            # Update info labels
            self.info_labels['invoice_number'].config(text=invoice['invoice_number'])
            self.info_labels['date'].config(text=invoice['created_at'][:16] if invoice['created_at'] else '-')
            self.info_labels['customer'].config(text=invoice.get('customer_name', 'نقدي / Cash'))
            self.info_labels['cashier'].config(text=invoice.get('cashier_name', '-'))
            self.info_labels['payment_method'].config(text=invoice.get('payment_method', '-'))
            self.info_labels['status'].config(text=invoice.get('status', '-'))

            self.info_labels['subtotal'].config(text=f"{invoice.get('subtotal', 0):.2f} QAR")
            self.info_labels['discount'].config(text=f"{invoice.get('discount_amount', 0):.2f} QAR")
            self.info_labels['tax'].config(text=f"{invoice.get('tax_amount', 0):.2f} QAR")
            self.info_labels['total'].config(text=f"{invoice.get('total_amount', 0):.2f} QAR")

            # Load items
            self.load_invoice_items(invoice)

        except Exception as e:
            self.logger.error(f"Error loading invoice details: {e}")

    def clear_invoice_details(self):
        """Clear invoice details"""
        self.selected_invoice_id = None

        for label in self.info_labels.values():
            label.config(text="-")

        # Clear items
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

    def load_invoice_items(self, invoice):
        """Load invoice items"""
        try:
            # Clear existing items
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)

            for item in invoice.get('items', []):
                item_name = item['name_en'] if self.settings.get('language') == 'en' else item['name_ar']

                self.items_tree.insert('', tk.END, values=(
                    item_name,
                    f"{item['quantity']:.1f}",
                    f"{item['unit_price']:.2f}",
                    f"{item['total_amount']:.2f}"
                ))

        except Exception as e:
            self.logger.error(f"Error loading invoice items: {e}")

    def view_invoice(self):
        """View selected invoice"""
        if not self.selected_invoice_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار فاتورة للعرض\nPlease select an invoice to view")
            return

        # For now, just show details in a message box
        # In a full implementation, this would open a detailed view window
        invoice = self.invoice_manager.get_invoice_by_id(self.selected_invoice_id)
        if invoice:
            details = f"""
فاتورة رقم: {invoice['invoice_number']}
التاريخ: {invoice['created_at'][:16] if invoice['created_at'] else '-'}
العميل: {invoice.get('customer_name', 'نقدي')}
المجموع: {invoice.get('total_amount', 0):.2f} QAR
الحالة: {invoice.get('status', '-')}

Invoice No: {invoice['invoice_number']}
Date: {invoice['created_at'][:16] if invoice['created_at'] else '-'}
Customer: {invoice.get('customer_name', 'Cash')}
Total: {invoice.get('total_amount', 0):.2f} QAR
Status: {invoice.get('status', '-')}
            """
            messagebox.showinfo("تفاصيل الفاتورة - Invoice Details", details)

    def print_pdf(self):
        """Print invoice as PDF"""
        if not self.selected_invoice_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار فاتورة للطباعة\nPlease select an invoice to print")
            return

        try:
            # Generate PDF
            pdf_path = self.invoice_printer.generate_invoice_pdf(self.selected_invoice_id)

            # Open PDF file
            if os.path.exists(pdf_path):
                if os.name == 'nt':  # Windows
                    os.startfile(pdf_path)
                else:  # macOS and Linux
                    subprocess.call(['open' if os.name == 'posix' else 'xdg-open', pdf_path])

                messagebox.showinfo("نجح - Success", f"تم إنشاء ملف PDF\nPDF generated: {pdf_path}")
            else:
                messagebox.showerror("خطأ - Error", "فشل في إنشاء ملف PDF\nFailed to generate PDF")

        except Exception as e:
            self.logger.error(f"Error printing PDF: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في طباعة PDF\nError printing PDF: {str(e)}")

    def print_thermal(self):
        """Print thermal receipt"""
        if not self.selected_invoice_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار فاتورة للطباعة\nPlease select an invoice to print")
            return

        try:
            if self.invoice_printer.print_thermal_receipt(self.selected_invoice_id):
                messagebox.showinfo("نجح - Success", "تم إنشاء الإيصال الحراري\nThermal receipt generated")
            else:
                messagebox.showerror("خطأ - Error", "فشل في طباعة الإيصال\nFailed to print receipt")

        except Exception as e:
            self.logger.error(f"Error printing thermal receipt: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في الطباعة الحرارية\nError printing thermal receipt: {str(e)}")

    # Placeholder methods for additional features
    def export_invoices(self):
        """Export invoices to file"""
        messagebox.showinfo("قريباً - Coming Soon", "تصدير الفواتير قيد التطوير\nInvoice export under development")

    def email_invoice(self):
        """Email invoice"""
        messagebox.showinfo("قريباً - Coming Soon", "إرسال الفواتير بالبريد قيد التطوير\nEmail invoice under development")

    def run(self):
        """Run the invoices window"""
        if self.window:
            self.window.mainloop()
