#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Products management window for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import os
from pathlib import Path

from core.product_manager import ProductManager, CategoryManager
from core.auth import auth_manager
from database.models import Product
from config.settings import Settings
from utils.logger import get_logger

class ProductsWindow:
    """Products management window"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.product_manager = ProductManager()
        self.category_manager = CategoryManager()
        self.settings = Settings()
        self.logger = get_logger()
        
        self.window = None
        self.selected_product_id = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the products management interface"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("إدارة المنتجات - Products Management")
        self.window.geometry("1000x700")
        
        # Create main layout
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
        
        # Load initial data
        self.load_categories()
        self.refresh_products()
        
    def create_toolbar(self):
        """Create toolbar with action buttons"""
        toolbar = ttk.Frame(self.window)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # Product actions
        ttk.Button(toolbar, text="منتج جديد - New Product", 
                  command=self.new_product).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تعديل - Edit", 
                  command=self.edit_product).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="حذف - Delete", 
                  command=self.delete_product).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Category actions
        ttk.Button(toolbar, text="فئة جديدة - New Category", 
                  command=self.new_category).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تحديث المخزون - Update Stock", 
                  command=self.update_stock).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Import/Export
        ttk.Button(toolbar, text="استيراد - Import", 
                  command=self.import_products).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تصدير - Export", 
                  command=self.export_products).pack(side=tk.LEFT, padx=(0, 5))
        
        # Refresh button
        ttk.Button(toolbar, text="تحديث - Refresh", 
                  command=self.refresh_products).pack(side=tk.RIGHT)
        
    def create_main_content(self):
        """Create main content area"""
        # Create paned window for resizable layout
        paned = ttk.PanedWindow(self.window, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Products list
        left_frame = ttk.Frame(paned)
        paned.add(left_frame, weight=2)
        
        self.create_products_list(left_frame)
        
        # Right panel - Product details
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)
        
        self.create_product_details(right_frame)
        
    def create_products_list(self, parent):
        """Create products list with search and filters"""
        # Search and filter section
        search_frame = ttk.LabelFrame(parent, text="البحث والتصفية - Search & Filter", padding="10")
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Search entry
        search_row1 = ttk.Frame(search_frame)
        search_row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(search_row1, text="البحث - Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_row1, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 5))
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        ttk.Button(search_row1, text="بحث - Search", 
                  command=self.search_products).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_row1, text="مسح - Clear", 
                  command=self.clear_search).pack(side=tk.LEFT)
        
        # Category filter
        search_row2 = ttk.Frame(search_frame)
        search_row2.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(search_row2, text="الفئة - Category:").pack(side=tk.LEFT)
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(search_row2, textvariable=self.category_var, 
                                         state="readonly", width=25)
        self.category_combo.pack(side=tk.LEFT, padx=(10, 5))
        self.category_combo.bind('<<ComboboxSelected>>', self.on_category_change)
        
        # Stock filter
        self.stock_filter_var = tk.StringVar(value="all")
        stock_filters = [
            ("الكل - All", "all"),
            ("مخزون منخفض - Low Stock", "low"),
            ("نفد المخزون - Out of Stock", "out")
        ]
        
        for text, value in stock_filters:
            ttk.Radiobutton(search_row2, text=text, variable=self.stock_filter_var, 
                          value=value, command=self.refresh_products).pack(side=tk.LEFT, padx=(10, 0))
        
        # Products list
        list_frame = ttk.LabelFrame(parent, text="قائمة المنتجات - Products List", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview for products
        columns = ('barcode', 'name_ar', 'name_en', 'category', 'price', 'stock', 'status')
        self.products_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.products_tree.heading('barcode', text='الباركود - Barcode')
        self.products_tree.heading('name_ar', text='الاسم بالعربية')
        self.products_tree.heading('name_en', text='Name in English')
        self.products_tree.heading('category', text='الفئة - Category')
        self.products_tree.heading('price', text='السعر - Price')
        self.products_tree.heading('stock', text='المخزون - Stock')
        self.products_tree.heading('status', text='الحالة - Status')
        
        self.products_tree.column('barcode', width=120)
        self.products_tree.column('name_ar', width=150)
        self.products_tree.column('name_en', width=150)
        self.products_tree.column('category', width=100)
        self.products_tree.column('price', width=80, anchor=tk.CENTER)
        self.products_tree.column('stock', width=80, anchor=tk.CENTER)
        self.products_tree.column('status', width=80, anchor=tk.CENTER)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.products_tree.xview)
        self.products_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.products_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # Bind selection event
        self.products_tree.bind('<<TreeviewSelect>>', self.on_product_select)
        self.products_tree.bind('<Double-1>', self.edit_product)
        
    def create_product_details(self, parent):
        """Create product details panel"""
        details_frame = ttk.LabelFrame(parent, text="تفاصيل المنتج - Product Details", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # Product image
        image_frame = ttk.Frame(details_frame)
        image_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.product_image_label = ttk.Label(image_frame, text="لا توجد صورة\nNo Image", 
                                           relief=tk.SUNKEN, anchor=tk.CENTER)
        self.product_image_label.pack(fill=tk.X, ipady=50)
        
        # Product info
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Create info labels
        self.info_labels = {}
        info_fields = [
            ('barcode', 'الباركود - Barcode'),
            ('name_ar', 'الاسم بالعربية'),
            ('name_en', 'Name in English'),
            ('category', 'الفئة - Category'),
            ('cost_price', 'سعر التكلفة - Cost Price'),
            ('selling_price', 'سعر البيع - Selling Price'),
            ('stock_quantity', 'الكمية - Quantity'),
            ('min_stock_level', 'الحد الأدنى - Min Level'),
            ('unit', 'الوحدة - Unit')
        ]
        
        for i, (field, label) in enumerate(info_fields):
            ttk.Label(info_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.info_labels[field] = ttk.Label(info_frame, text="-", relief=tk.SUNKEN, width=20)
            self.info_labels[field].grid(row=i, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=2)
        
        info_frame.grid_columnconfigure(1, weight=1)
        
        # Stock movements
        movements_frame = ttk.LabelFrame(details_frame, text="حركات المخزون - Stock Movements", padding="5")
        movements_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Movements treeview
        movements_columns = ('date', 'type', 'quantity', 'reference', 'user')
        self.movements_tree = ttk.Treeview(movements_frame, columns=movements_columns, 
                                         show='headings', height=8)
        
        self.movements_tree.heading('date', text='التاريخ - Date')
        self.movements_tree.heading('type', text='النوع - Type')
        self.movements_tree.heading('quantity', text='الكمية - Qty')
        self.movements_tree.heading('reference', text='المرجع - Ref')
        self.movements_tree.heading('user', text='المستخدم - User')
        
        self.movements_tree.column('date', width=100)
        self.movements_tree.column('type', width=60)
        self.movements_tree.column('quantity', width=60, anchor=tk.CENTER)
        self.movements_tree.column('reference', width=80)
        self.movements_tree.column('user', width=80)
        
        movements_scrollbar = ttk.Scrollbar(movements_frame, orient=tk.VERTICAL, 
                                          command=self.movements_tree.yview)
        self.movements_tree.configure(yscrollcommand=movements_scrollbar.set)
        
        self.movements_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        movements_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_status_bar(self):
        """Create status bar"""
        status_frame = ttk.Frame(self.window)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar(value="جاهز - Ready")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # Products count
        self.products_count_var = tk.StringVar(value="المنتجات: 0")
        ttk.Label(status_frame, textvariable=self.products_count_var).pack(side=tk.RIGHT, padx=5)

    def load_categories(self):
        """Load categories into combobox"""
        try:
            categories = self.category_manager.get_all_categories()
            category_values = ["الكل - All"]

            for category in categories:
                name = category['name_ar'] if self.settings.get('language') == 'ar' else category['name_en']
                category_values.append(f"{name} ({category['id']})")

            self.category_combo['values'] = category_values
            self.category_combo.set("الكل - All")

        except Exception as e:
            self.logger.error(f"Error loading categories: {e}")

    def refresh_products(self):
        """Refresh products list"""
        try:
            # Clear existing items
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # Get filter criteria
            search_query = self.search_var.get().strip()
            category_filter = self.category_var.get()
            stock_filter = self.stock_filter_var.get()

            # Extract category ID if selected
            category_id = None
            if category_filter and category_filter != "الكل - All":
                try:
                    category_id = int(category_filter.split('(')[-1].split(')')[0])
                except:
                    pass

            # Get products based on filters
            if search_query:
                products = self.product_manager.search_products(search_query, category_id)
            else:
                # Get all products (we'll implement this method)
                products = self.get_all_products(category_id)

            # Apply stock filter
            if stock_filter == "low":
                products = [p for p in products if p['stock_quantity'] <= p['min_stock_level']]
            elif stock_filter == "out":
                products = [p for p in products if p['stock_quantity'] == 0]

            # Populate treeview
            for product in products:
                # Determine status
                if product['stock_quantity'] == 0:
                    status = "نفد"  # Out of stock
                elif product['stock_quantity'] <= product['min_stock_level']:
                    status = "منخفض"  # Low stock
                else:
                    status = "متوفر"  # Available

                # Get category name
                category_name = product.get('category_name_ar', '') or product.get('category_name_en', '') or '-'

                values = (
                    product['barcode'] or '-',
                    product['name_ar'],
                    product['name_en'],
                    category_name,
                    f"{product['selling_price']:.2f}",
                    str(product['stock_quantity']),
                    status
                )

                # Color coding based on stock status
                tags = []
                if product['stock_quantity'] == 0:
                    tags = ['out_of_stock']
                elif product['stock_quantity'] <= product['min_stock_level']:
                    tags = ['low_stock']

                item = self.products_tree.insert('', tk.END, values=values, tags=tags)
                # Store product ID in item
                self.products_tree.set(item, '#0', product['id'])

            # Configure tag colors
            self.products_tree.tag_configure('out_of_stock', background='#ffcccc')
            self.products_tree.tag_configure('low_stock', background='#fff3cd')

            # Update products count
            self.products_count_var.set(f"المنتجات: {len(products)}")
            self.status_var.set("تم تحديث قائمة المنتجات - Products list updated")

        except Exception as e:
            self.logger.error(f"Error refreshing products: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في تحديث المنتجات\nError refreshing products: {str(e)}")

    def get_all_products(self, category_id=None):
        """Get all products with optional category filter"""
        try:
            with self.product_manager.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                sql = """
                    SELECT p.*, c.name_ar as category_name_ar, c.name_en as category_name_en
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1
                """
                params = []

                if category_id:
                    sql += " AND p.category_id = ?"
                    params.append(category_id)

                sql += " ORDER BY p.name_en"

                cursor.execute(sql, params)
                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"Error getting all products: {e}")
            return []

    def on_search_change(self, event=None):
        """Handle search text change"""
        # Auto-search after a short delay
        if hasattr(self, '_search_timer'):
            self.window.after_cancel(self._search_timer)
        self._search_timer = self.window.after(500, self.search_products)

    def search_products(self):
        """Search products"""
        self.refresh_products()

    def clear_search(self):
        """Clear search and filters"""
        self.search_var.set("")
        self.category_combo.set("الكل - All")
        self.stock_filter_var.set("all")
        self.refresh_products()

    def on_category_change(self, event=None):
        """Handle category selection change"""
        self.refresh_products()

    def on_product_select(self, event=None):
        """Handle product selection"""
        selection = self.products_tree.selection()
        if not selection:
            self.clear_product_details()
            return

        item = selection[0]
        product_id = self.products_tree.set(item, '#0')

        try:
            product_id = int(product_id)
            self.selected_product_id = product_id
            self.load_product_details(product_id)

        except (ValueError, TypeError):
            self.clear_product_details()

    def load_product_details(self, product_id):
        """Load product details"""
        try:
            product = self.product_manager.get_product_by_id(product_id)
            if not product:
                self.clear_product_details()
                return

            # Update info labels
            self.info_labels['barcode'].config(text=product['barcode'] or '-')
            self.info_labels['name_ar'].config(text=product['name_ar'])
            self.info_labels['name_en'].config(text=product['name_en'])

            category_name = product.get('category_name_ar', '') or product.get('category_name_en', '') or '-'
            self.info_labels['category'].config(text=category_name)

            self.info_labels['cost_price'].config(text=f"{product['cost_price']:.2f} QAR")
            self.info_labels['selling_price'].config(text=f"{product['selling_price']:.2f} QAR")
            self.info_labels['stock_quantity'].config(text=str(product['stock_quantity']))
            self.info_labels['min_stock_level'].config(text=str(product['min_stock_level']))
            self.info_labels['unit'].config(text=product['unit'])

            # Load stock movements
            self.load_stock_movements(product_id)

            # Try to load product image
            self.load_product_image(product_id)

        except Exception as e:
            self.logger.error(f"Error loading product details: {e}")

    def clear_product_details(self):
        """Clear product details"""
        self.selected_product_id = None

        for label in self.info_labels.values():
            label.config(text="-")

        # Clear movements
        for item in self.movements_tree.get_children():
            self.movements_tree.delete(item)

        # Clear image
        self.product_image_label.config(text="لا توجد صورة\nNo Image", image="")

    def load_stock_movements(self, product_id):
        """Load stock movements for product"""
        try:
            # Clear existing movements
            for item in self.movements_tree.get_children():
                self.movements_tree.delete(item)

            movements = self.product_manager.get_stock_movements(product_id, 20)

            for movement in movements:
                date_str = movement['created_at'][:16] if movement['created_at'] else '-'
                movement_type = movement['movement_type']
                quantity = f"{movement['quantity']:.1f}"
                reference = movement['reference_type'] or '-'
                user_name = movement['user_name'] or '-'

                self.movements_tree.insert('', tk.END, values=(date_str, movement_type, quantity, reference, user_name))

        except Exception as e:
            self.logger.error(f"Error loading stock movements: {e}")

    def load_product_image(self, product_id):
        """Load product image if exists"""
        try:
            # Look for product image in assets/products/
            image_path = Path(f"assets/products/{product_id}.jpg")
            if not image_path.exists():
                image_path = Path(f"assets/products/{product_id}.png")

            if image_path.exists():
                image = Image.open(image_path)
                image = image.resize((150, 150), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(image)

                self.product_image_label.config(image=photo, text="")
                self.product_image_label.image = photo  # Keep a reference
            else:
                self.product_image_label.config(text="لا توجد صورة\nNo Image", image="")

        except Exception as e:
            self.logger.error(f"Error loading product image: {e}")
            self.product_image_label.config(text="لا توجد صورة\nNo Image", image="")

    def new_product(self):
        """Create new product"""
        if not auth_manager.can_access_feature('product_management'):
            messagebox.showerror("خطأ - Error", "غير مصرح لك بإدارة المنتجات\nNot authorized for product management")
            return

        dialog = ProductDialog(self.window, "منتج جديد - New Product")
        if dialog.result:
            try:
                product = Product(**dialog.result)
                if self.product_manager.create_product(product):
                    messagebox.showinfo("نجح - Success", "تم إنشاء المنتج بنجاح\nProduct created successfully")
                    self.refresh_products()
                else:
                    messagebox.showerror("خطأ - Error", "فشل في إنشاء المنتج\nFailed to create product")
            except Exception as e:
                self.logger.error(f"Error creating product: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في إنشاء المنتج\nError creating product: {str(e)}")

    def edit_product(self):
        """Edit selected product"""
        if not self.selected_product_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار منتج للتعديل\nPlease select a product to edit")
            return

        if not auth_manager.can_access_feature('product_management'):
            messagebox.showerror("خطأ - Error", "غير مصرح لك بإدارة المنتجات\nNot authorized for product management")
            return

        try:
            product = self.product_manager.get_product_by_id(self.selected_product_id)
            if not product:
                messagebox.showerror("خطأ - Error", "المنتج غير موجود\nProduct not found")
                return

            dialog = ProductDialog(self.window, "تعديل المنتج - Edit Product", product)
            if dialog.result:
                if self.product_manager.update_product(self.selected_product_id, dialog.result):
                    messagebox.showinfo("نجح - Success", "تم تحديث المنتج بنجاح\nProduct updated successfully")
                    self.refresh_products()
                    self.load_product_details(self.selected_product_id)
                else:
                    messagebox.showerror("خطأ - Error", "فشل في تحديث المنتج\nFailed to update product")

        except Exception as e:
            self.logger.error(f"Error editing product: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في تعديل المنتج\nError editing product: {str(e)}")

    def delete_product(self):
        """Delete selected product"""
        if not self.selected_product_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار منتج للحذف\nPlease select a product to delete")
            return

        if not auth_manager.can_access_feature('product_management'):
            messagebox.showerror("خطأ - Error", "غير مصرح لك بإدارة المنتجات\nNot authorized for product management")
            return

        if messagebox.askyesno("تأكيد الحذف - Confirm Delete",
                              "هل أنت متأكد من حذف هذا المنتج؟\nAre you sure you want to delete this product?"):
            try:
                # Soft delete by setting is_active to False
                if self.product_manager.update_product(self.selected_product_id, {'is_active': False}):
                    messagebox.showinfo("نجح - Success", "تم حذف المنتج بنجاح\nProduct deleted successfully")
                    self.refresh_products()
                    self.clear_product_details()
                else:
                    messagebox.showerror("خطأ - Error", "فشل في حذف المنتج\nFailed to delete product")

            except Exception as e:
                self.logger.error(f"Error deleting product: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في حذف المنتج\nError deleting product: {str(e)}")

    def new_category(self):
        """Create new category"""
        if not auth_manager.can_access_feature('product_management'):
            messagebox.showerror("خطأ - Error", "غير مصرح لك بإدارة المنتجات\nNot authorized for product management")
            return

        dialog = CategoryDialog(self.window)
        if dialog.result:
            try:
                if self.category_manager.create_category(**dialog.result):
                    messagebox.showinfo("نجح - Success", "تم إنشاء الفئة بنجاح\nCategory created successfully")
                    self.load_categories()
                else:
                    messagebox.showerror("خطأ - Error", "فشل في إنشاء الفئة\nFailed to create category")
            except Exception as e:
                self.logger.error(f"Error creating category: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في إنشاء الفئة\nError creating category: {str(e)}")

    def update_stock(self):
        """Update stock for selected product"""
        if not self.selected_product_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار منتج لتحديث المخزون\nPlease select a product to update stock")
            return

        if not auth_manager.can_access_feature('inventory'):
            messagebox.showerror("خطأ - Error", "غير مصرح لك بإدارة المخزون\nNot authorized for inventory management")
            return

        dialog = StockUpdateDialog(self.window, self.selected_product_id)
        if dialog.result:
            try:
                quantity_change = dialog.result['quantity_change']
                movement_type = dialog.result['movement_type']
                notes = dialog.result['notes']

                if self.product_manager.update_stock(self.selected_product_id, quantity_change,
                                                   movement_type, 'manual_adjustment', None, notes):
                    messagebox.showinfo("نجح - Success", "تم تحديث المخزون بنجاح\nStock updated successfully")
                    self.refresh_products()
                    self.load_product_details(self.selected_product_id)
                else:
                    messagebox.showerror("خطأ - Error", "فشل في تحديث المخزون\nFailed to update stock")

            except Exception as e:
                self.logger.error(f"Error updating stock: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في تحديث المخزون\nError updating stock: {str(e)}")

    def import_products(self):
        """Import products from file"""
        messagebox.showinfo("قريباً - Coming Soon", "استيراد المنتجات قيد التطوير\nProduct import under development")

    def export_products(self):
        """Export products to file"""
        messagebox.showinfo("قريباً - Coming Soon", "تصدير المنتجات قيد التطوير\nProduct export under development")

    def run(self):
        """Run the products window"""
        if self.window:
            self.window.mainloop()
