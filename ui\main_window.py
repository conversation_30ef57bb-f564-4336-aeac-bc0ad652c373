#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main POS window for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import threading

from core.auth import auth_manager
from core.product_manager import ProductManager
from core.customer_manager import CustomerManager
from core.invoice_manager import InvoiceManager
from database.models import PaymentMethod
from config.settings import Settings
from utils.logger import get_logger

class MainWindow:
    """Main POS application window"""
    
    def __init__(self):
        self.settings = Settings()
        self.logger = get_logger()
        self.product_manager = ProductManager()
        self.customer_manager = CustomerManager()
        self.invoice_manager = InvoiceManager()
        
        # Current transaction state
        self.current_invoice_id = None
        self.cart_items = []
        self.selected_customer = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the main user interface"""
        self.root = tk.Tk()
        self.root.title("Qatar POS System - نظام نقاط البيع القطري")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # Maximize window
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Create main layout
        self.create_menu_bar()
        self.create_main_layout()
        self.create_status_bar()
        
        # Initialize new transaction
        self.new_transaction()
        
        # Bind keyboard shortcuts
        self.setup_keyboard_shortcuts()
        
    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف - File", menu=file_menu)
        file_menu.add_command(label="معاملة جديدة - New Transaction", command=self.new_transaction, accelerator="F1")
        file_menu.add_separator()
        file_menu.add_command(label="خروج - Exit", command=self.exit_application, accelerator="Alt+F4")
        
        # Sales menu
        sales_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مبيعات - Sales", menu=sales_menu)
        sales_menu.add_command(label="نقطة البيع - Point of Sale", command=self.focus_barcode_entry)
        sales_menu.add_command(label="الفواتير - Invoices", command=self.open_invoices_window)
        
        # Inventory menu
        if auth_manager.can_access_feature('inventory'):
            inventory_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="مخزون - Inventory", menu=inventory_menu)
            inventory_menu.add_command(label="المنتجات - Products", command=self.open_products_window)
            inventory_menu.add_command(label="الفئات - Categories", command=self.open_categories_window)
            inventory_menu.add_command(label="تقرير المخزون - Stock Report", command=self.open_stock_report)
        
        # Customers menu
        if auth_manager.can_access_feature('customers'):
            customers_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="عملاء - Customers", menu=customers_menu)
            customers_menu.add_command(label="إدارة العملاء - Manage Customers", command=self.open_customers_window)
        
        # Reports menu
        if auth_manager.can_access_feature('reports'):
            reports_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="تقارير - Reports", menu=reports_menu)
            reports_menu.add_command(label="تقرير المبيعات - Sales Report", command=self.open_sales_report)
            reports_menu.add_command(label="تقرير الأرباح - Profit Report", command=self.open_profit_report)
        
        # Settings menu
        if auth_manager.can_access_feature('system_settings'):
            settings_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="إعدادات - Settings", menu=settings_menu)
            settings_menu.add_command(label="إعدادات النظام - System Settings", command=self.open_system_settings)
            settings_menu.add_command(label="إدارة المستخدمين - User Management", command=self.open_user_management)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة - Help", menu=help_menu)
        help_menu.add_command(label="حول - About", command=self.show_about)
        
    def create_main_layout(self):
        """Create main application layout"""
        # Main container
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Product search and cart
        left_panel = ttk.Frame(main_container, width=400)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        left_panel.pack_propagate(False)
        
        self.create_product_search_section(left_panel)
        self.create_cart_section(left_panel)
        
        # Right panel - Customer info and payment
        right_panel = ttk.Frame(main_container, width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_panel.pack_propagate(False)
        
        self.create_customer_section(right_panel)
        self.create_payment_section(right_panel)
        
    def create_product_search_section(self, parent):
        """Create product search section"""
        search_frame = ttk.LabelFrame(parent, text="البحث عن المنتجات - Product Search", padding="10")
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Barcode entry
        ttk.Label(search_frame, text="الباركود أو اسم المنتج - Barcode or Product Name:").pack(anchor=tk.W)
        
        entry_frame = ttk.Frame(search_frame)
        entry_frame.pack(fill=tk.X, pady=(5, 10))
        
        self.barcode_var = tk.StringVar()
        self.barcode_entry = ttk.Entry(entry_frame, textvariable=self.barcode_var, font=("Arial", 12))
        self.barcode_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.barcode_entry.bind('<Return>', self.search_product)
        self.barcode_entry.bind('<KeyRelease>', self.on_search_key_release)
        
        search_btn = ttk.Button(entry_frame, text="بحث - Search", command=self.search_product)
        search_btn.pack(side=tk.RIGHT)
        
        # Product list
        ttk.Label(search_frame, text="المنتجات - Products:").pack(anchor=tk.W, pady=(10, 5))
        
        # Treeview for products
        columns = ('name', 'price', 'stock')
        self.products_tree = ttk.Treeview(search_frame, columns=columns, show='headings', height=8)
        
        self.products_tree.heading('name', text='اسم المنتج - Name')
        self.products_tree.heading('price', text='السعر - Price')
        self.products_tree.heading('stock', text='المخزون - Stock')
        
        self.products_tree.column('name', width=200)
        self.products_tree.column('price', width=80, anchor=tk.CENTER)
        self.products_tree.column('stock', width=80, anchor=tk.CENTER)
        
        # Scrollbar for products tree
        products_scrollbar = ttk.Scrollbar(search_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        products_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind double-click to add product
        self.products_tree.bind('<Double-1>', self.add_product_to_cart)
        
    def create_cart_section(self, parent):
        """Create shopping cart section"""
        cart_frame = ttk.LabelFrame(parent, text="سلة التسوق - Shopping Cart", padding="10")
        cart_frame.pack(fill=tk.BOTH, expand=True)
        
        # Cart items tree
        cart_columns = ('name', 'qty', 'price', 'total')
        self.cart_tree = ttk.Treeview(cart_frame, columns=cart_columns, show='headings', height=10)
        
        self.cart_tree.heading('name', text='المنتج - Product')
        self.cart_tree.heading('qty', text='الكمية - Qty')
        self.cart_tree.heading('price', text='السعر - Price')
        self.cart_tree.heading('total', text='المجموع - Total')
        
        self.cart_tree.column('name', width=180)
        self.cart_tree.column('qty', width=60, anchor=tk.CENTER)
        self.cart_tree.column('price', width=80, anchor=tk.CENTER)
        self.cart_tree.column('total', width=80, anchor=tk.CENTER)
        
        # Scrollbar for cart
        cart_scrollbar = ttk.Scrollbar(cart_frame, orient=tk.VERTICAL, command=self.cart_tree.yview)
        self.cart_tree.configure(yscrollcommand=cart_scrollbar.set)
        
        self.cart_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        cart_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Cart buttons
        cart_buttons_frame = ttk.Frame(cart_frame)
        cart_buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(cart_buttons_frame, text="حذف - Remove", command=self.remove_from_cart).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(cart_buttons_frame, text="تعديل الكمية - Edit Qty", command=self.edit_quantity).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(cart_buttons_frame, text="مسح الكل - Clear All", command=self.clear_cart).pack(side=tk.LEFT)
        
        # Total display
        total_frame = ttk.Frame(cart_frame)
        total_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.total_var = tk.StringVar(value="0.00")
        ttk.Label(total_frame, text="المجموع الكلي - Total:", font=("Arial", 12, "bold")).pack(side=tk.LEFT)
        ttk.Label(total_frame, textvariable=self.total_var, font=("Arial", 14, "bold"), foreground="blue").pack(side=tk.RIGHT)
        
    def create_customer_section(self, parent):
        """Create customer selection section"""
        customer_frame = ttk.LabelFrame(parent, text="العميل - Customer", padding="10")
        customer_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Customer search
        ttk.Label(customer_frame, text="رقم الهاتف - Phone Number:").pack(anchor=tk.W)
        
        phone_frame = ttk.Frame(customer_frame)
        phone_frame.pack(fill=tk.X, pady=(5, 10))
        
        self.customer_phone_var = tk.StringVar()
        customer_phone_entry = ttk.Entry(phone_frame, textvariable=self.customer_phone_var)
        customer_phone_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        customer_phone_entry.bind('<Return>', self.search_customer)
        
        ttk.Button(phone_frame, text="بحث - Search", command=self.search_customer).pack(side=tk.RIGHT)
        
        # Customer info display
        self.customer_info_var = tk.StringVar(value="لا يوجد عميل محدد\nNo customer selected")
        customer_info_label = ttk.Label(customer_frame, textvariable=self.customer_info_var, 
                                      justify=tk.LEFT, relief=tk.SUNKEN, padding="5")
        customer_info_label.pack(fill=tk.X, pady=(0, 10))
        
        # Customer buttons
        customer_buttons_frame = ttk.Frame(customer_frame)
        customer_buttons_frame.pack(fill=tk.X)
        
        ttk.Button(customer_buttons_frame, text="عميل جديد - New Customer",
                  command=self.create_new_customer).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(customer_buttons_frame, text="مسح - Clear",
                  command=self.clear_customer).pack(side=tk.LEFT)

    def create_payment_section(self, parent):
        """Create payment section"""
        payment_frame = ttk.LabelFrame(parent, text="الدفع - Payment", padding="10")
        payment_frame.pack(fill=tk.BOTH, expand=True)

        # Payment method
        ttk.Label(payment_frame, text="طريقة الدفع - Payment Method:").pack(anchor=tk.W, pady=(0, 5))

        self.payment_method_var = tk.StringVar(value="cash")
        payment_methods = [
            ("نقدي - Cash", "cash"),
            ("بطاقة - Card", "card"),
            ("آجل - Credit", "credit")
        ]

        for text, value in payment_methods:
            ttk.Radiobutton(payment_frame, text=text, variable=self.payment_method_var,
                          value=value).pack(anchor=tk.W)

        # Discount
        ttk.Label(payment_frame, text="الخصم - Discount:").pack(anchor=tk.W, pady=(10, 5))

        discount_frame = ttk.Frame(payment_frame)
        discount_frame.pack(fill=tk.X, pady=(0, 10))

        self.discount_var = tk.StringVar(value="0.00")
        discount_entry = ttk.Entry(discount_frame, textvariable=self.discount_var, width=10)
        discount_entry.pack(side=tk.LEFT)
        ttk.Label(discount_frame, text="QAR").pack(side=tk.LEFT, padx=(5, 0))

        # Notes
        ttk.Label(payment_frame, text="ملاحظات - Notes:").pack(anchor=tk.W, pady=(10, 5))
        self.notes_text = tk.Text(payment_frame, height=3, wrap=tk.WORD)
        self.notes_text.pack(fill=tk.X, pady=(0, 10))

        # Action buttons
        buttons_frame = ttk.Frame(payment_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        # Complete sale button
        complete_btn = ttk.Button(buttons_frame, text="إتمام البيع - Complete Sale",
                                command=self.complete_sale, style="Accent.TButton")
        complete_btn.pack(fill=tk.X, pady=(0, 5))

        # Other buttons
        button_row1 = ttk.Frame(buttons_frame)
        button_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_row1, text="طباعة - Print", command=self.print_invoice).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        ttk.Button(button_row1, text="حفظ - Save", command=self.save_invoice).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(2, 0))

        button_row2 = ttk.Frame(buttons_frame)
        button_row2.pack(fill=tk.X)

        ttk.Button(button_row2, text="معاملة جديدة - New Transaction", command=self.new_transaction).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        ttk.Button(button_row2, text="إلغاء - Cancel", command=self.cancel_transaction).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(2, 0))

    def create_status_bar(self):
        """Create status bar"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        # User info
        user = auth_manager.get_current_user()
        user_info = f"المستخدم - User: {user.full_name} ({user.role.value})"
        ttk.Label(status_frame, text=user_info).pack(side=tk.LEFT, padx=5)

        # Date and time
        self.datetime_var = tk.StringVar()
        self.update_datetime()
        ttk.Label(status_frame, textvariable=self.datetime_var).pack(side=tk.RIGHT, padx=5)

    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts"""
        self.root.bind('<F1>', lambda e: self.new_transaction())
        self.root.bind('<F2>', lambda e: self.focus_barcode_entry())
        self.root.bind('<F3>', lambda e: self.search_customer())
        self.root.bind('<F12>', lambda e: self.complete_sale())
        self.root.bind('<Escape>', lambda e: self.cancel_transaction())

    def focus_barcode_entry(self):
        """Focus on barcode entry field"""
        self.barcode_entry.focus_set()

    def update_datetime(self):
        """Update date and time display"""
        now = datetime.now()
        datetime_str = now.strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_var.set(datetime_str)

        # Schedule next update
        self.root.after(1000, self.update_datetime)

    def new_transaction(self):
        """Start a new transaction"""
        try:
            # Clear current state
            self.clear_cart()
            self.clear_customer()
            self.discount_var.set("0.00")
            self.notes_text.delete(1.0, tk.END)
            self.payment_method_var.set("cash")

            # Create new invoice
            self.current_invoice_id = self.invoice_manager.create_invoice()

            if self.current_invoice_id:
                self.logger.info(f"New transaction started: Invoice ID {self.current_invoice_id}")
                self.focus_barcode_entry()
            else:
                messagebox.showerror("خطأ - Error", "فشل في إنشاء معاملة جديدة\nFailed to create new transaction")

        except Exception as e:
            self.logger.error(f"Error starting new transaction: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في إنشاء معاملة جديدة\nError creating new transaction: {str(e)}")

    def search_product(self, event=None):
        """Search for products"""
        query = self.barcode_var.get().strip()
        if not query:
            return

        try:
            # Clear previous results
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # Search by barcode first
            product = self.product_manager.get_product_by_barcode(query)
            if product:
                # Add directly to cart if found by barcode
                self.add_product_to_cart_by_data(product)
                self.barcode_var.set("")
                return

            # Search by name
            products = self.product_manager.search_products(query)

            for product in products:
                name = product['name_en'] if self.settings.get('language') == 'en' else product['name_ar']
                price = f"{product['selling_price']:.2f}"
                stock = str(product['stock_quantity'])

                self.products_tree.insert('', tk.END, values=(name, price, stock), tags=(product['id'],))

        except Exception as e:
            self.logger.error(f"Error searching products: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في البحث\nSearch error: {str(e)}")

    def on_search_key_release(self, event):
        """Handle key release in search entry"""
        # Auto-search after a short delay
        if hasattr(self, '_search_timer'):
            self.root.after_cancel(self._search_timer)
        self._search_timer = self.root.after(500, self.search_product)

    def add_product_to_cart(self, event=None):
        """Add selected product to cart"""
        selection = self.products_tree.selection()
        if not selection:
            return

        item = self.products_tree.item(selection[0])
        product_id = int(item['tags'][0])

        try:
            product = self.product_manager.get_product_by_id(product_id)
            if product:
                self.add_product_to_cart_by_data(product)

        except Exception as e:
            self.logger.error(f"Error adding product to cart: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في إضافة المنتج\nError adding product: {str(e)}")

    def add_product_to_cart_by_data(self, product, quantity=1):
        """Add product to cart by product data"""
        try:
            if not self.current_invoice_id:
                messagebox.showerror("خطأ - Error", "لا توجد معاملة نشطة\nNo active transaction")
                return

            # Check stock
            if product['stock_quantity'] < quantity:
                messagebox.showerror("خطأ - Error",
                                   f"مخزون غير كافي\nInsufficient stock\nAvailable: {product['stock_quantity']}")
                return

            # Add to invoice
            if self.invoice_manager.add_item_to_invoice(
                self.current_invoice_id, product['id'], quantity, product['selling_price']
            ):
                self.refresh_cart()
                self.barcode_var.set("")  # Clear search
                self.logger.info(f"Product added to cart: {product['name_en']}")
            else:
                messagebox.showerror("خطأ - Error", "فشل في إضافة المنتج\nFailed to add product")

        except Exception as e:
            self.logger.error(f"Error adding product to cart: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في إضافة المنتج\nError adding product: {str(e)}")

    def refresh_cart(self):
        """Refresh cart display"""
        try:
            # Clear cart tree
            for item in self.cart_tree.get_children():
                self.cart_tree.delete(item)

            if not self.current_invoice_id:
                self.total_var.set("0.00 QAR")
                return

            # Get invoice with items
            invoice = self.invoice_manager.get_invoice_by_id(self.current_invoice_id)
            if not invoice:
                self.total_var.set("0.00 QAR")
                return

            total = 0.0

            for item in invoice.get('items', []):
                name = item['name_en'] if self.settings.get('language') == 'en' else item['name_ar']
                qty = f"{item['quantity']:.1f}"
                price = f"{item['unit_price']:.2f}"
                item_total = item['total_amount']
                total += item_total

                self.cart_tree.insert('', tk.END, values=(name, qty, price, f"{item_total:.2f}"),
                                    tags=(item['id'],))

            self.total_var.set(f"{total:.2f} QAR")

        except Exception as e:
            self.logger.error(f"Error refreshing cart: {e}")

    # Placeholder methods for menu actions
    def open_invoices_window(self):
        messagebox.showinfo("قريباً - Coming Soon", "نافذة الفواتير قيد التطوير\nInvoices window under development")

    def open_products_window(self):
        messagebox.showinfo("قريباً - Coming Soon", "نافذة المنتجات قيد التطوير\nProducts window under development")

    def open_categories_window(self):
        messagebox.showinfo("قريباً - Coming Soon", "نافذة الفئات قيد التطوير\nCategories window under development")

    def open_stock_report(self):
        messagebox.showinfo("قريباً - Coming Soon", "تقرير المخزون قيد التطوير\nStock report under development")

    def open_customers_window(self):
        messagebox.showinfo("قريباً - Coming Soon", "نافذة العملاء قيد التطوير\nCustomers window under development")

    def open_sales_report(self):
        messagebox.showinfo("قريباً - Coming Soon", "تقرير المبيعات قيد التطوير\nSales report under development")

    def open_profit_report(self):
        messagebox.showinfo("قريباً - Coming Soon", "تقرير الأرباح قيد التطوير\nProfit report under development")

    def open_system_settings(self):
        messagebox.showinfo("قريباً - Coming Soon", "إعدادات النظام قيد التطوير\nSystem settings under development")

    def open_user_management(self):
        messagebox.showinfo("قريباً - Coming Soon", "إدارة المستخدمين قيد التطوير\nUser management under development")

    def show_about(self):
        messagebox.showinfo("حول - About",
                          "Qatar POS System v1.0\nنظام نقاط البيع القطري\n\n"
                          "Developed for Qatar retail market\nمطور للسوق القطري")

    # Placeholder methods for cart and payment actions
    def remove_from_cart(self):
        messagebox.showinfo("قريباً - Coming Soon", "حذف من السلة قيد التطوير\nRemove from cart under development")

    def edit_quantity(self):
        messagebox.showinfo("قريباً - Coming Soon", "تعديل الكمية قيد التطوير\nEdit quantity under development")

    def clear_cart(self):
        """Clear shopping cart"""
        for item in self.cart_tree.get_children():
            self.cart_tree.delete(item)
        self.total_var.set("0.00 QAR")

    def search_customer(self, event=None):
        messagebox.showinfo("قريباً - Coming Soon", "البحث عن العملاء قيد التطوير\nCustomer search under development")

    def create_new_customer(self):
        messagebox.showinfo("قريباً - Coming Soon", "إنشاء عميل جديد قيد التطوير\nNew customer creation under development")

    def clear_customer(self):
        """Clear selected customer"""
        self.selected_customer = None
        self.customer_info_var.set("لا يوجد عميل محدد\nNo customer selected")
        self.customer_phone_var.set("")

    def complete_sale(self):
        """Complete the current sale"""
        try:
            if not self.current_invoice_id:
                messagebox.showerror("خطأ - Error", "لا توجد معاملة نشطة\nNo active transaction")
                return

            # Get payment method
            payment_method_map = {
                'cash': PaymentMethod.CASH,
                'card': PaymentMethod.CARD,
                'credit': PaymentMethod.CREDIT
            }
            payment_method = payment_method_map[self.payment_method_var.get()]

            # Get discount
            try:
                discount = float(self.discount_var.get())
            except ValueError:
                discount = 0.0

            # Get notes
            notes = self.notes_text.get(1.0, tk.END).strip()

            # Complete the invoice
            if self.invoice_manager.complete_invoice(self.current_invoice_id, payment_method, discount, notes):
                messagebox.showinfo("نجح - Success", "تم إتمام البيع بنجاح\nSale completed successfully")
                self.new_transaction()  # Start new transaction
            else:
                messagebox.showerror("خطأ - Error", "فشل في إتمام البيع\nFailed to complete sale")

        except Exception as e:
            self.logger.error(f"Error completing sale: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في إتمام البيع\nError completing sale: {str(e)}")

    def print_invoice(self):
        messagebox.showinfo("قريباً - Coming Soon", "طباعة الفاتورة قيد التطوير\nInvoice printing under development")

    def save_invoice(self):
        messagebox.showinfo("قريباً - Coming Soon", "حفظ الفاتورة قيد التطوير\nSave invoice under development")

    def cancel_transaction(self):
        """Cancel current transaction"""
        if messagebox.askyesno("تأكيد - Confirm", "هل تريد إلغاء المعاملة الحالية؟\nCancel current transaction?"):
            self.new_transaction()

    def exit_application(self):
        """Exit the application"""
        if messagebox.askyesno("تأكيد - Confirm", "هل تريد الخروج من النظام؟\nExit the system?"):
            auth_manager.logout()
            self.root.destroy()

    def run(self):
        """Run the main window"""
        self.root.mainloop()
