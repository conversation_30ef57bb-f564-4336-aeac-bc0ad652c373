#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Product management dialogs for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import os
from pathlib import Path

from core.product_manager import CategoryManager
from config.settings import Settings
from utils.logger import get_logger

class ProductDialog:
    """Product creation/editing dialog"""
    
    def __init__(self, parent, title, product_data=None):
        self.parent = parent
        self.product_data = product_data or {}
        self.category_manager = CategoryManager()
        self.settings = Settings()
        self.logger = get_logger()
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.setup_ui()
        self.load_data()
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
    def setup_ui(self):
        """Setup dialog UI"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Product info section
        info_frame = ttk.LabelFrame(main_frame, text="معلومات المنتج - Product Information", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Barcode
        ttk.Label(info_frame, text="الباركود - Barcode:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.barcode_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.barcode_var, width=30).grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Arabic name
        ttk.Label(info_frame, text="الاسم بالعربية - Arabic Name: *").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.name_ar_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.name_ar_var, width=30).grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # English name
        ttk.Label(info_frame, text="English Name: *").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.name_en_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.name_en_var, width=30).grid(row=2, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Description
        ttk.Label(info_frame, text="الوصف - Description:").grid(row=3, column=0, sticky=tk.W+tk.N, pady=5)
        self.description_text = tk.Text(info_frame, height=3, width=30)
        self.description_text.grid(row=3, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Category
        ttk.Label(info_frame, text="الفئة - Category:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(info_frame, textvariable=self.category_var, state="readonly", width=27)
        self.category_combo.grid(row=4, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        info_frame.grid_columnconfigure(1, weight=1)
        
        # Pricing section
        pricing_frame = ttk.LabelFrame(main_frame, text="الأسعار - Pricing", padding="10")
        pricing_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Cost price
        ttk.Label(pricing_frame, text="سعر التكلفة - Cost Price:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.cost_price_var = tk.StringVar(value="0.00")
        cost_frame = ttk.Frame(pricing_frame)
        cost_frame.grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        ttk.Entry(cost_frame, textvariable=self.cost_price_var, width=15).pack(side=tk.LEFT)
        ttk.Label(cost_frame, text="QAR").pack(side=tk.LEFT, padx=(5, 0))
        
        # Selling price
        ttk.Label(pricing_frame, text="سعر البيع - Selling Price: *").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.selling_price_var = tk.StringVar(value="0.00")
        selling_frame = ttk.Frame(pricing_frame)
        selling_frame.grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        ttk.Entry(selling_frame, textvariable=self.selling_price_var, width=15).pack(side=tk.LEFT)
        ttk.Label(selling_frame, text="QAR").pack(side=tk.LEFT, padx=(5, 0))
        
        pricing_frame.grid_columnconfigure(1, weight=1)
        
        # Inventory section
        inventory_frame = ttk.LabelFrame(main_frame, text="المخزون - Inventory", padding="10")
        inventory_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Stock quantity
        ttk.Label(inventory_frame, text="الكمية الحالية - Current Quantity:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.stock_quantity_var = tk.StringVar(value="0")
        ttk.Entry(inventory_frame, textvariable=self.stock_quantity_var, width=15).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Minimum stock level
        ttk.Label(inventory_frame, text="الحد الأدنى - Minimum Level:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.min_stock_var = tk.StringVar(value="0")
        ttk.Entry(inventory_frame, textvariable=self.min_stock_var, width=15).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Unit
        ttk.Label(inventory_frame, text="الوحدة - Unit:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.unit_var = tk.StringVar(value="piece")
        unit_combo = ttk.Combobox(inventory_frame, textvariable=self.unit_var, width=12)
        unit_combo['values'] = ["piece", "kg", "liter", "meter", "box", "pack"]
        unit_combo.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="حفظ - Save", command=self.save_product).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء - Cancel", command=self.cancel).pack(side=tk.RIGHT)
        
        # Load categories
        self.load_categories()
        
    def load_categories(self):
        """Load categories into combobox"""
        try:
            categories = self.category_manager.get_all_categories()
            category_values = []
            
            for category in categories:
                name = category['name_ar'] if self.settings.get('language') == 'ar' else category['name_en']
                category_values.append(f"{name} ({category['id']})")
                
            self.category_combo['values'] = category_values
            
        except Exception as e:
            self.logger.error(f"Error loading categories: {e}")
            
    def load_data(self):
        """Load existing product data if editing"""
        if not self.product_data:
            return
            
        self.barcode_var.set(self.product_data.get('barcode', ''))
        self.name_ar_var.set(self.product_data.get('name_ar', ''))
        self.name_en_var.set(self.product_data.get('name_en', ''))
        
        description = self.product_data.get('description', '')
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, description)
        
        self.cost_price_var.set(str(self.product_data.get('cost_price', 0.0)))
        self.selling_price_var.set(str(self.product_data.get('selling_price', 0.0)))
        self.stock_quantity_var.set(str(self.product_data.get('stock_quantity', 0)))
        self.min_stock_var.set(str(self.product_data.get('min_stock_level', 0)))
        self.unit_var.set(self.product_data.get('unit', 'piece'))
        
        # Set category
        category_id = self.product_data.get('category_id')
        if category_id:
            for value in self.category_combo['values']:
                if f"({category_id})" in value:
                    self.category_combo.set(value)
                    break
                    
    def save_product(self):
        """Save product data"""
        try:
            # Validate required fields
            if not self.name_ar_var.get().strip():
                messagebox.showerror("خطأ - Error", "يرجى إدخال الاسم بالعربية\nPlease enter Arabic name")
                return
                
            if not self.name_en_var.get().strip():
                messagebox.showerror("خطأ - Error", "يرجى إدخال الاسم بالإنجليزية\nPlease enter English name")
                return
                
            try:
                selling_price = float(self.selling_price_var.get())
                if selling_price <= 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("خطأ - Error", "يرجى إدخال سعر بيع صحيح\nPlease enter valid selling price")
                return
                
            # Extract category ID
            category_id = None
            category_value = self.category_var.get()
            if category_value:
                try:
                    category_id = int(category_value.split('(')[-1].split(')')[0])
                except:
                    pass
                    
            # Prepare result data
            self.result = {
                'barcode': self.barcode_var.get().strip() or None,
                'name_ar': self.name_ar_var.get().strip(),
                'name_en': self.name_en_var.get().strip(),
                'description': self.description_text.get(1.0, tk.END).strip(),
                'category_id': category_id,
                'cost_price': float(self.cost_price_var.get() or 0),
                'selling_price': float(self.selling_price_var.get()),
                'stock_quantity': int(self.stock_quantity_var.get() or 0),
                'min_stock_level': int(self.min_stock_var.get() or 0),
                'unit': self.unit_var.get()
            }
            
            self.dialog.destroy()
            
        except Exception as e:
            self.logger.error(f"Error saving product: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في حفظ البيانات\nError saving data: {str(e)}")
            
    def cancel(self):
        """Cancel dialog"""
        self.result = None
        self.dialog.destroy()

class CategoryDialog:
    """Category creation dialog"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("فئة جديدة - New Category")
        self.dialog.geometry("400x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.setup_ui()
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
    def setup_ui(self):
        """Setup dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Arabic name
        ttk.Label(main_frame, text="الاسم بالعربية - Arabic Name: *").pack(anchor=tk.W, pady=(0, 5))
        self.name_ar_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.name_ar_var, width=40).pack(fill=tk.X, pady=(0, 15))
        
        # English name
        ttk.Label(main_frame, text="English Name: *").pack(anchor=tk.W, pady=(0, 5))
        self.name_en_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.name_en_var, width=40).pack(fill=tk.X, pady=(0, 15))
        
        # Description
        ttk.Label(main_frame, text="الوصف - Description:").pack(anchor=tk.W, pady=(0, 5))
        self.description_text = tk.Text(main_frame, height=4, width=40)
        self.description_text.pack(fill=tk.X, pady=(0, 15))
        
        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="حفظ - Save", command=self.save_category).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء - Cancel", command=self.cancel).pack(side=tk.RIGHT)
        
    def save_category(self):
        """Save category data"""
        name_ar = self.name_ar_var.get().strip()
        name_en = self.name_en_var.get().strip()
        
        if not name_ar:
            messagebox.showerror("خطأ - Error", "يرجى إدخال الاسم بالعربية\nPlease enter Arabic name")
            return
            
        if not name_en:
            messagebox.showerror("خطأ - Error", "يرجى إدخال الاسم بالإنجليزية\nPlease enter English name")
            return
            
        self.result = {
            'name_ar': name_ar,
            'name_en': name_en,
            'description': self.description_text.get(1.0, tk.END).strip()
        }
        
        self.dialog.destroy()
        
    def cancel(self):
        """Cancel dialog"""
        self.result = None
        self.dialog.destroy()

class StockUpdateDialog:
    """Stock update dialog"""
    
    def __init__(self, parent, product_id):
        self.parent = parent
        self.product_id = product_id
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("تحديث المخزون - Update Stock")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.setup_ui()
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
    def setup_ui(self):
        """Setup dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Movement type
        ttk.Label(main_frame, text="نوع الحركة - Movement Type:").pack(anchor=tk.W, pady=(0, 5))
        self.movement_type_var = tk.StringVar(value="in")
        
        movement_frame = ttk.Frame(main_frame)
        movement_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Radiobutton(movement_frame, text="إضافة - Add Stock", 
                       variable=self.movement_type_var, value="in").pack(side=tk.LEFT)
        ttk.Radiobutton(movement_frame, text="خصم - Remove Stock", 
                       variable=self.movement_type_var, value="out").pack(side=tk.LEFT, padx=(20, 0))
        
        # Quantity
        ttk.Label(main_frame, text="الكمية - Quantity:").pack(anchor=tk.W, pady=(0, 5))
        self.quantity_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.quantity_var, width=20).pack(anchor=tk.W, pady=(0, 15))
        
        # Notes
        ttk.Label(main_frame, text="ملاحظات - Notes:").pack(anchor=tk.W, pady=(0, 5))
        self.notes_text = tk.Text(main_frame, height=4, width=40)
        self.notes_text.pack(fill=tk.X, pady=(0, 15))
        
        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="تحديث - Update", command=self.update_stock).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء - Cancel", command=self.cancel).pack(side=tk.RIGHT)
        
    def update_stock(self):
        """Update stock"""
        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ - Error", "يرجى إدخال كمية صحيحة\nPlease enter valid quantity")
            return
            
        movement_type = self.movement_type_var.get()
        
        # Convert to quantity change
        quantity_change = quantity if movement_type == "in" else -quantity
        
        self.result = {
            'quantity_change': quantity_change,
            'movement_type': movement_type,
            'notes': self.notes_text.get(1.0, tk.END).strip()
        }
        
        self.dialog.destroy()
        
    def cancel(self):
        """Cancel dialog"""
        self.result = None
        self.dialog.destroy()
