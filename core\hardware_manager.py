#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hardware integration manager for Qatar POS System
"""

import time
import threading
from typing import Optional, Callable, Dict, Any
import tkinter as tk
from pathlib import Path

# Try to import serial, make it optional
try:
    import serial
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    print("⚠️  Warning: pyserial not installed. Hardware features will be disabled.")
    print("   To enable hardware support, run: pip install pyserial")

from config.settings import Settings
from utils.logger import get_logger

class BarcodeScanner:
    """Barcode scanner interface"""
    
    def __init__(self, callback: Optional[Callable[[str], None]] = None):
        self.settings = Settings()
        self.logger = get_logger()
        self.callback = callback
        self.serial_connection = None
        self.is_running = False
        self.scan_thread = None
        
    def start(self) -> bool:
        """Start barcode scanner"""
        try:
            if not self.settings.get_bool('barcode_scanner', 'HARDWARE', False):
                self.logger.info("Barcode scanner disabled in settings")
                return False

            if not SERIAL_AVAILABLE:
                self.logger.warning("Serial library not available, using keyboard mode")
                self._setup_keyboard_scanner()
                return True

            port = self.settings.get('scanner_port', 'HARDWARE', 'COM2')

            # Try to open serial connection
            try:
                self.serial_connection = serial.Serial(
                    port=port,
                    baudrate=9600,
                    timeout=1
                )
                self.is_running = True

                # Start scanning thread
                self.scan_thread = threading.Thread(target=self._scan_loop, daemon=True)
                self.scan_thread.start()

                self.logger.info(f"Barcode scanner started on {port}")
                return True

            except Exception as e:  # Changed from serial.SerialException
                self.logger.warning(f"Could not open scanner port {port}: {e}")
                # Fall back to keyboard input mode
                self._setup_keyboard_scanner()
                return True
                
        except Exception as e:
            self.logger.error(f"Error starting barcode scanner: {e}")
            return False
            
    def stop(self):
        """Stop barcode scanner"""
        self.is_running = False
        
        if self.serial_connection and self.serial_connection.is_open:
            self.serial_connection.close()
            
        if self.scan_thread and self.scan_thread.is_alive():
            self.scan_thread.join(timeout=2)
            
        self.logger.info("Barcode scanner stopped")
        
    def _scan_loop(self):
        """Main scanning loop for serial scanners"""
        buffer = ""
        
        while self.is_running and self.serial_connection and self.serial_connection.is_open:
            try:
                if self.serial_connection.in_waiting > 0:
                    data = self.serial_connection.read(self.serial_connection.in_waiting).decode('utf-8', errors='ignore')
                    buffer += data
                    
                    # Check for complete barcode (usually ends with newline or carriage return)
                    if '\n' in buffer or '\r' in buffer:
                        barcode = buffer.strip()
                        if barcode and self.callback:
                            self.callback(barcode)
                        buffer = ""
                        
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Error reading from barcode scanner: {e}")
                time.sleep(1)
                
    def _setup_keyboard_scanner(self):
        """Setup keyboard wedge scanner (fallback mode)"""
        self.logger.info("Using keyboard wedge mode for barcode scanner")
        # In keyboard wedge mode, the scanner acts like a keyboard
        # The main application should handle this through normal key events

class ThermalPrinter:
    """Thermal printer interface"""
    
    def __init__(self):
        self.settings = Settings()
        self.logger = get_logger()
        self.serial_connection = None
        
    def connect(self) -> bool:
        """Connect to thermal printer"""
        try:
            if not self.settings.get_bool('thermal_printer', 'HARDWARE', False):
                self.logger.info("Thermal printer disabled in settings")
                return False

            if not SERIAL_AVAILABLE:
                self.logger.warning("Serial library not available, thermal printer disabled")
                return False

            port = self.settings.get('printer_port', 'HARDWARE', 'COM1')

            try:
                self.serial_connection = serial.Serial(
                    port=port,
                    baudrate=9600,
                    timeout=2
                )

                # Test printer connection
                self._send_command(b'\x1B\x40')  # Initialize printer
                time.sleep(0.5)

                self.logger.info(f"Thermal printer connected on {port}")
                return True

            except Exception as e:  # Changed from serial.SerialException
                self.logger.warning(f"Could not connect to thermal printer on {port}: {e}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to thermal printer: {e}")
            return False
            
    def disconnect(self):
        """Disconnect from thermal printer"""
        if self.serial_connection and self.serial_connection.is_open:
            self.serial_connection.close()
            self.logger.info("Thermal printer disconnected")
            
    def print_receipt(self, receipt_text: str) -> bool:
        """Print receipt text"""
        try:
            if not self.serial_connection or not self.serial_connection.is_open:
                if not self.connect():
                    return False
                    
            # Convert text to bytes and send to printer
            receipt_bytes = receipt_text.encode('utf-8', errors='ignore')
            
            # Send receipt data
            self._send_command(receipt_bytes)
            
            # Cut paper (if supported)
            self._send_command(b'\x1D\x56\x42\x00')  # Partial cut
            
            # Feed paper
            self._send_command(b'\n\n\n')
            
            self.logger.info("Receipt printed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error printing receipt: {e}")
            return False
            
    def _send_command(self, command: bytes):
        """Send command to printer"""
        if self.serial_connection and self.serial_connection.is_open:
            self.serial_connection.write(command)
            self.serial_connection.flush()

class CashDrawer:
    """Cash drawer interface"""
    
    def __init__(self):
        self.settings = Settings()
        self.logger = get_logger()
        self.printer = None
        
    def open_drawer(self) -> bool:
        """Open cash drawer"""
        try:
            if not self.settings.get_bool('cash_drawer', 'HARDWARE', False):
                self.logger.info("Cash drawer disabled in settings")
                return False
                
            # Most cash drawers are connected through the printer
            if not self.printer:
                self.printer = ThermalPrinter()
                
            if not self.printer.connect():
                self.logger.warning("Could not connect to printer for cash drawer")
                return False
                
            # Send cash drawer open command (ESC/POS standard)
            drawer_command = b'\x1B\x70\x00\x19\xFA'  # Open drawer pulse
            self.printer._send_command(drawer_command)
            
            self.logger.info("Cash drawer opened")
            return True
            
        except Exception as e:
            self.logger.error(f"Error opening cash drawer: {e}")
            return False

class HardwareManager:
    """Main hardware manager"""
    
    def __init__(self):
        self.settings = Settings()
        self.logger = get_logger()
        
        self.barcode_scanner = None
        self.thermal_printer = None
        self.cash_drawer = None
        
        self.barcode_callback = None
        
    def initialize(self, barcode_callback: Optional[Callable[[str], None]] = None) -> Dict[str, bool]:
        """Initialize all hardware components"""
        results = {}
        
        try:
            # Initialize barcode scanner
            if self.settings.get_bool('barcode_scanner', 'HARDWARE', False):
                self.barcode_scanner = BarcodeScanner(barcode_callback)
                results['barcode_scanner'] = self.barcode_scanner.start()
            else:
                results['barcode_scanner'] = False
                
            # Initialize thermal printer
            if self.settings.get_bool('thermal_printer', 'HARDWARE', False):
                self.thermal_printer = ThermalPrinter()
                results['thermal_printer'] = self.thermal_printer.connect()
            else:
                results['thermal_printer'] = False
                
            # Initialize cash drawer
            if self.settings.get_bool('cash_drawer', 'HARDWARE', False):
                self.cash_drawer = CashDrawer()
                results['cash_drawer'] = True  # Cash drawer doesn't need initialization
            else:
                results['cash_drawer'] = False
                
            self.logger.info(f"Hardware initialization results: {results}")
            return results
            
        except Exception as e:
            self.logger.error(f"Error initializing hardware: {e}")
            return {'barcode_scanner': False, 'thermal_printer': False, 'cash_drawer': False}
            
    def shutdown(self):
        """Shutdown all hardware components"""
        try:
            if self.barcode_scanner:
                self.barcode_scanner.stop()
                
            if self.thermal_printer:
                self.thermal_printer.disconnect()
                
            self.logger.info("Hardware components shut down")
            
        except Exception as e:
            self.logger.error(f"Error shutting down hardware: {e}")
            
    def print_receipt(self, receipt_text: str) -> bool:
        """Print receipt using thermal printer"""
        if self.thermal_printer:
            return self.thermal_printer.print_receipt(receipt_text)
        else:
            self.logger.warning("Thermal printer not available")
            return False
            
    def open_cash_drawer(self) -> bool:
        """Open cash drawer"""
        if self.cash_drawer:
            return self.cash_drawer.open_drawer()
        else:
            self.logger.warning("Cash drawer not available")
            return False
            
    def test_hardware(self) -> Dict[str, bool]:
        """Test all hardware components"""
        results = {}
        
        try:
            # Test barcode scanner
            if self.barcode_scanner:
                results['barcode_scanner'] = self.barcode_scanner.is_running
            else:
                results['barcode_scanner'] = False
                
            # Test thermal printer
            if self.thermal_printer:
                test_receipt = """
=== HARDWARE TEST ===
Thermal Printer Test
اختبار الطابعة الحرارية

Date: {date}
Time: {time}

Test successful!
الاختبار نجح!
==================
                """.format(
                    date=time.strftime('%Y-%m-%d'),
                    time=time.strftime('%H:%M:%S')
                )
                results['thermal_printer'] = self.thermal_printer.print_receipt(test_receipt)
            else:
                results['thermal_printer'] = False
                
            # Test cash drawer
            if self.cash_drawer:
                results['cash_drawer'] = self.cash_drawer.open_drawer()
            else:
                results['cash_drawer'] = False
                
            self.logger.info(f"Hardware test results: {results}")
            return results
            
        except Exception as e:
            self.logger.error(f"Error testing hardware: {e}")
            return {'barcode_scanner': False, 'thermal_printer': False, 'cash_drawer': False}

# Global hardware manager instance
hardware_manager = HardwareManager()
