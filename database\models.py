#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database models for Qatar POS System
"""

import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from enum import Enum

class UserRole(Enum):
    """User roles in the system"""
    MANAGER = "manager"
    SELLER = "seller"
    ACCOUNTANT = "accountant"
    INVENTORY = "inventory"

class PaymentMethod(Enum):
    """Payment methods"""
    CASH = "cash"
    CARD = "card"
    CREDIT = "credit"

class InvoiceStatus(Enum):
    """Invoice status"""
    PENDING = "pending"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    RETURNED = "returned"

@dataclass
class User:
    """User model"""
    id: Optional[int] = None
    username: str = ""
    password_hash: str = ""
    full_name: str = ""
    role: UserRole = UserRole.SELLER
    email: str = ""
    phone: str = ""
    is_active: bool = True
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None

@dataclass
class Category:
    """Product category model"""
    id: Optional[int] = None
    name_ar: str = ""
    name_en: str = ""
    description: str = ""
    is_active: bool = True
    created_at: Optional[datetime] = None

@dataclass
class Product:
    """Product model"""
    id: Optional[int] = None
    barcode: str = ""
    name_ar: str = ""
    name_en: str = ""
    description: str = ""
    category_id: Optional[int] = None
    cost_price: float = 0.0
    selling_price: float = 0.0
    stock_quantity: int = 0
    min_stock_level: int = 0
    unit: str = "piece"
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

@dataclass
class Customer:
    """Customer model"""
    id: Optional[int] = None
    name: str = ""
    phone: str = ""
    email: str = ""
    national_id: str = ""
    address: str = ""
    credit_limit: float = 0.0
    current_balance: float = 0.0
    is_active: bool = True
    created_at: Optional[datetime] = None

@dataclass
class Invoice:
    """Invoice model"""
    id: Optional[int] = None
    invoice_number: str = ""
    customer_id: Optional[int] = None
    user_id: int = 0
    subtotal: float = 0.0
    tax_amount: float = 0.0
    discount_amount: float = 0.0
    total_amount: float = 0.0
    payment_method: PaymentMethod = PaymentMethod.CASH
    status: InvoiceStatus = InvoiceStatus.PENDING
    notes: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

@dataclass
class InvoiceItem:
    """Invoice item model"""
    id: Optional[int] = None
    invoice_id: int = 0
    product_id: int = 0
    quantity: float = 0.0
    unit_price: float = 0.0
    discount_amount: float = 0.0
    total_amount: float = 0.0

@dataclass
class StockMovement:
    """Stock movement model"""
    id: Optional[int] = None
    product_id: int = 0
    movement_type: str = ""  # 'in', 'out', 'adjustment'
    quantity: float = 0.0
    reference_type: str = ""  # 'sale', 'purchase', 'adjustment'
    reference_id: Optional[int] = None
    notes: str = ""
    user_id: int = 0
    created_at: Optional[datetime] = None

@dataclass
class SystemSetting:
    """System settings model"""
    id: Optional[int] = None
    key: str = ""
    value: str = ""
    description: str = ""
    updated_at: Optional[datetime] = None
