#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive test suite for Qatar POS System
"""

import unittest
import sys
import os
from pathlib import Path
import tempfile
import shutil
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestDatabaseOperations(unittest.TestCase):
    """Test database operations"""
    
    def setUp(self):
        """Set up test database"""
        from database.database_manager import DatabaseManager
        
        # Create temporary database
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db_manager = DatabaseManager(self.test_db_path)
        self.db_manager.initialize_database()
        
    def tearDown(self):
        """Clean up test database"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
            
    def test_database_initialization(self):
        """Test database initialization"""
        # Check if tables exist
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = [
                'users', 'categories', 'products', 'customers', 
                'invoices', 'invoice_items', 'stock_movements', 'system_settings'
            ]
            
            for table in expected_tables:
                self.assertIn(table, tables, f"Table {table} not found")
                
    def test_user_creation(self):
        """Test user creation and authentication"""
        from core.auth import AuthManager
        from database.models import User, UserRole
        
        auth_manager = AuthManager(self.db_manager)
        
        # Create test user
        user_data = {
            'username': 'testuser',
            'password': 'testpass123',
            'full_name': 'Test User',
            'email': '<EMAIL>',
            'role': UserRole.SELLER
        }
        
        user_id = auth_manager.create_user(**user_data)
        self.assertIsNotNone(user_id, "User creation failed")
        
        # Test authentication
        self.assertTrue(auth_manager.authenticate('testuser', 'testpass123'))
        self.assertFalse(auth_manager.authenticate('testuser', 'wrongpass'))

class TestProductManagement(unittest.TestCase):
    """Test product management operations"""
    
    def setUp(self):
        """Set up test environment"""
        from database.database_manager import DatabaseManager
        from core.product_manager import ProductManager, CategoryManager
        
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db_manager = DatabaseManager(self.test_db_path)
        self.db_manager.initialize_database()
        
        self.product_manager = ProductManager(self.db_manager)
        self.category_manager = CategoryManager(self.db_manager)
        
    def tearDown(self):
        """Clean up"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
            
    def test_category_operations(self):
        """Test category CRUD operations"""
        # Create category
        category_id = self.category_manager.create_category(
            name_ar="مشروبات",
            name_en="Beverages",
            description="Test category"
        )
        self.assertIsNotNone(category_id)
        
        # Get category
        category = self.category_manager.get_category_by_id(category_id)
        self.assertIsNotNone(category)
        self.assertEqual(category['name_en'], "Beverages")
        
        # Update category
        updated = self.category_manager.update_category(
            category_id, 
            {'description': 'Updated description'}
        )
        self.assertTrue(updated)
        
    def test_product_operations(self):
        """Test product CRUD operations"""
        from database.models import Product
        
        # Create category first
        category_id = self.category_manager.create_category(
            name_ar="طعام",
            name_en="Food"
        )
        
        # Create product
        product = Product(
            barcode="1234567890",
            name_ar="تفاح",
            name_en="Apple",
            category_id=category_id,
            cost_price=1.0,
            selling_price=2.0,
            stock_quantity=100,
            min_stock_level=10,
            unit="kg"
        )
        
        product_id = self.product_manager.create_product(product)
        self.assertIsNotNone(product_id)
        
        # Get product
        retrieved_product = self.product_manager.get_product_by_id(product_id)
        self.assertIsNotNone(retrieved_product)
        self.assertEqual(retrieved_product['name_en'], "Apple")
        
        # Search product
        search_results = self.product_manager.search_products("Apple")
        self.assertGreater(len(search_results), 0)
        
        # Update stock
        stock_updated = self.product_manager.update_stock(
            product_id, -10, 'out', 'sale', None, 'Test sale'
        )
        self.assertTrue(stock_updated)
        
        # Check updated stock
        updated_product = self.product_manager.get_product_by_id(product_id)
        self.assertEqual(updated_product['stock_quantity'], 90)

class TestInvoiceManagement(unittest.TestCase):
    """Test invoice management operations"""
    
    def setUp(self):
        """Set up test environment"""
        from database.database_manager import DatabaseManager
        from core.invoice_manager import InvoiceManager
        from core.product_manager import ProductManager
        from core.customer_manager import CustomerManager
        from core.auth import AuthManager
        from database.models import User, UserRole, Product, Customer
        
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db_manager = DatabaseManager(self.test_db_path)
        self.db_manager.initialize_database()
        
        self.invoice_manager = InvoiceManager(self.db_manager)
        self.product_manager = ProductManager(self.db_manager)
        self.customer_manager = CustomerManager(self.db_manager)
        self.auth_manager = AuthManager(self.db_manager)
        
        # Create test user
        self.user_id = self.auth_manager.create_user(
            username='testuser',
            password='testpass',
            full_name='Test User',
            role=UserRole.SELLER
        )
        self.auth_manager.authenticate('testuser', 'testpass')
        
        # Create test product
        product = Product(
            name_ar="منتج تجريبي",
            name_en="Test Product",
            cost_price=5.0,
            selling_price=10.0,
            stock_quantity=100,
            unit="piece"
        )
        self.product_id = self.product_manager.create_product(product)
        
        # Create test customer
        customer = Customer(
            name="Test Customer",
            phone="+974 1234 5678",
            email="<EMAIL>"
        )
        self.customer_id = self.customer_manager.create_customer(customer)
        
    def tearDown(self):
        """Clean up"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
            
    def test_invoice_creation_and_completion(self):
        """Test complete invoice workflow"""
        from database.models import PaymentMethod
        
        # Create invoice
        invoice_id = self.invoice_manager.create_invoice(self.customer_id)
        self.assertIsNotNone(invoice_id)
        
        # Add item to invoice
        item_added = self.invoice_manager.add_item_to_invoice(
            invoice_id, self.product_id, 2, 10.0
        )
        self.assertTrue(item_added)
        
        # Complete invoice
        completed = self.invoice_manager.complete_invoice(
            invoice_id, PaymentMethod.CASH, 0.0, "Test invoice"
        )
        self.assertTrue(completed)
        
        # Verify invoice
        invoice = self.invoice_manager.get_invoice_by_id(invoice_id)
        self.assertIsNotNone(invoice)
        self.assertEqual(invoice['status'], 'completed')
        self.assertEqual(len(invoice['items']), 1)
        self.assertEqual(invoice['total_amount'], 20.0)
        
        # Verify stock was updated
        product = self.product_manager.get_product_by_id(self.product_id)
        self.assertEqual(product['stock_quantity'], 98)

class TestReportsGeneration(unittest.TestCase):
    """Test reports generation"""
    
    def setUp(self):
        """Set up test environment with sample data"""
        from database.database_manager import DatabaseManager
        from core.reports_manager import ReportsManager
        from core.invoice_manager import InvoiceManager
        from core.product_manager import ProductManager
        from core.auth import AuthManager
        from database.models import UserRole, Product, PaymentMethod
        
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db_manager = DatabaseManager(self.test_db_path)
        self.db_manager.initialize_database()
        
        self.reports_manager = ReportsManager()
        self.reports_manager.db_manager = self.db_manager
        
        # Create test data
        auth_manager = AuthManager(self.db_manager)
        user_id = auth_manager.create_user(
            username='testuser',
            password='testpass',
            full_name='Test User',
            role=UserRole.MANAGER
        )
        auth_manager.authenticate('testuser', 'testpass')
        
        product_manager = ProductManager(self.db_manager)
        invoice_manager = InvoiceManager(self.db_manager)
        
        # Create test product
        product = Product(
            name_ar="منتج تجريبي",
            name_en="Test Product",
            cost_price=5.0,
            selling_price=10.0,
            stock_quantity=100,
            unit="piece"
        )
        product_id = product_manager.create_product(product)
        
        # Create test invoice
        invoice_id = invoice_manager.create_invoice()
        invoice_manager.add_item_to_invoice(invoice_id, product_id, 2, 10.0)
        invoice_manager.complete_invoice(invoice_id, PaymentMethod.CASH, 0.0, "Test")
        
    def tearDown(self):
        """Clean up"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
            
    def test_sales_report_generation(self):
        """Test sales report generation"""
        today = datetime.now().strftime('%Y-%m-%d')
        
        report = self.reports_manager.get_sales_report(today, today, 'day')
        
        self.assertIsNotNone(report)
        self.assertIn('sales_data', report)
        self.assertIn('summary', report)
        self.assertIn('payment_methods', report)
        
        # Check summary data
        summary = report['summary']
        self.assertGreater(summary['total_invoices'], 0)
        self.assertGreater(summary['total_sales'], 0)

class TestLocalization(unittest.TestCase):
    """Test localization features"""
    
    def test_currency_formatting(self):
        """Test currency formatting"""
        from utils.localization import qatar_localization
        
        # Test Arabic formatting
        arabic_formatted = qatar_localization.format_currency(1234.56, 'ar')
        self.assertIn('ر.ق', arabic_formatted)
        self.assertIn('1,234.56', arabic_formatted)
        
        # Test English formatting
        english_formatted = qatar_localization.format_currency(1234.56, 'en')
        self.assertIn('QAR', english_formatted)
        self.assertIn('1,234.56', english_formatted)
        
    def test_date_formatting(self):
        """Test date formatting"""
        from utils.localization import qatar_localization
        from datetime import date
        
        test_date = date(2024, 1, 15)
        
        # Test Gregorian formatting
        gregorian_ar = qatar_localization.format_gregorian_date(test_date, 'ar')
        self.assertIn('يناير', gregorian_ar)
        
        gregorian_en = qatar_localization.format_gregorian_date(test_date, 'en')
        self.assertIn('January', gregorian_en)
        
    def test_text_direction(self):
        """Test text direction detection"""
        from utils.localization import qatar_localization
        
        self.assertTrue(qatar_localization.is_rtl('ar'))
        self.assertFalse(qatar_localization.is_rtl('en'))
        
        self.assertEqual(qatar_localization.get_text_direction('ar'), 'rtl')
        self.assertEqual(qatar_localization.get_text_direction('en'), 'ltr')

class TestHardwareIntegration(unittest.TestCase):
    """Test hardware integration (mock tests)"""
    
    def test_hardware_manager_initialization(self):
        """Test hardware manager initialization"""
        from core.hardware_manager import HardwareManager
        
        hardware_manager = HardwareManager()
        
        # Test initialization (should not fail even without hardware)
        results = hardware_manager.initialize()
        self.assertIsInstance(results, dict)
        self.assertIn('barcode_scanner', results)
        self.assertIn('thermal_printer', results)
        self.assertIn('cash_drawer', results)
        
    def test_receipt_generation(self):
        """Test receipt text generation"""
        from core.invoice_printer import InvoicePrinter
        
        printer = InvoicePrinter()
        
        # Mock invoice data
        mock_invoice = {
            'invoice_number': 'TEST-001',
            'created_at': '2024-01-15 10:30:00',
            'cashier_name': 'Test Cashier',
            'customer_name': 'Test Customer',
            'items': [
                {
                    'name_en': 'Test Product',
                    'quantity': 2,
                    'unit_price': 10.0,
                    'total_amount': 20.0
                }
            ],
            'subtotal': 20.0,
            'discount_amount': 0.0,
            'tax_amount': 0.0,
            'total_amount': 20.0
        }
        
        shop_config = {
            'shop_name_ar': 'متجر تجريبي',
            'shop_name_en': 'Test Shop',
            'phone': '+974 1234 5678'
        }
        
        receipt_text = printer._generate_receipt_text(mock_invoice, shop_config)
        
        self.assertIn('TEST-001', receipt_text)
        self.assertIn('Test Product', receipt_text)
        self.assertIn('20.00', receipt_text)

def run_all_tests():
    """Run all tests"""
    print("🧪 Running Qatar POS System Test Suite")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestDatabaseOperations,
        TestProductManagement,
        TestInvoiceManagement,
        TestReportsGeneration,
        TestLocalization,
        TestHardwareIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 All tests passed!")
        print(f"✅ Ran {result.testsRun} tests successfully")
        return True
    else:
        print("❌ Some tests failed!")
        print(f"🔍 Ran {result.testsRun} tests")
        print(f"❌ Failures: {len(result.failures)}")
        print(f"💥 Errors: {len(result.errors)}")
        
        if result.failures:
            print("\nFailures:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
                
        if result.errors:
            print("\nErrors:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
                
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
