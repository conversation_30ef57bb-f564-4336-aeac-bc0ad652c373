#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Localization utilities for Qatar POS System
"""

import locale
from datetime import datetime, date
from typing import Dict, Any, Optional
import calendar

from config.settings import Settings
from utils.logger import get_logger

class QatarLocalization:
    """Qatar-specific localization utilities"""
    
    def __init__(self):
        self.settings = Settings()
        self.logger = get_logger()
        self.current_language = self.settings.get('language', 'APP', 'ar')
        
        # Arabic month names
        self.arabic_months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]
        
        # Hijri month names
        self.hijri_months = [
            'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
            'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
        ]
        
        # Arabic day names
        self.arabic_days = [
            'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
        ]
        
        # Common translations
        self.translations = {
            'ar': {
                'currency_symbol': 'ر.ق',
                'currency_code': 'QAR',
                'date_format': '%d/%m/%Y',
                'time_format': '%H:%M',
                'datetime_format': '%d/%m/%Y %H:%M',
                'decimal_separator': '.',
                'thousands_separator': ',',
                'yes': 'نعم',
                'no': 'لا',
                'ok': 'موافق',
                'cancel': 'إلغاء',
                'save': 'حفظ',
                'delete': 'حذف',
                'edit': 'تعديل',
                'add': 'إضافة',
                'search': 'بحث',
                'total': 'المجموع',
                'subtotal': 'المجموع الفرعي',
                'discount': 'الخصم',
                'tax': 'الضريبة',
                'quantity': 'الكمية',
                'price': 'السعر',
                'amount': 'المبلغ',
                'customer': 'العميل',
                'product': 'المنتج',
                'invoice': 'الفاتورة',
                'receipt': 'الإيصال',
                'cash': 'نقدي',
                'card': 'بطاقة',
                'credit': 'آجل'
            },
            'en': {
                'currency_symbol': 'QAR',
                'currency_code': 'QAR',
                'date_format': '%d/%m/%Y',
                'time_format': '%H:%M',
                'datetime_format': '%d/%m/%Y %H:%M',
                'decimal_separator': '.',
                'thousands_separator': ',',
                'yes': 'Yes',
                'no': 'No',
                'ok': 'OK',
                'cancel': 'Cancel',
                'save': 'Save',
                'delete': 'Delete',
                'edit': 'Edit',
                'add': 'Add',
                'search': 'Search',
                'total': 'Total',
                'subtotal': 'Subtotal',
                'discount': 'Discount',
                'tax': 'Tax',
                'quantity': 'Quantity',
                'price': 'Price',
                'amount': 'Amount',
                'customer': 'Customer',
                'product': 'Product',
                'invoice': 'Invoice',
                'receipt': 'Receipt',
                'cash': 'Cash',
                'card': 'Card',
                'credit': 'Credit'
            }
        }
        
    def get_text(self, key: str, language: Optional[str] = None) -> str:
        """Get localized text"""
        if language is None:
            language = self.current_language
            
        return self.translations.get(language, {}).get(key, key)
        
    def format_currency(self, amount: float, language: Optional[str] = None) -> str:
        """Format currency amount according to Qatar standards"""
        if language is None:
            language = self.current_language
            
        # Format number with proper separators
        formatted_amount = f"{amount:,.2f}"
        
        if language == 'ar':
            # Arabic formatting: ر.ق 1,234.56
            return f"ر.ق {formatted_amount}"
        else:
            # English formatting: QAR 1,234.56
            return f"QAR {formatted_amount}"
            
    def format_date(self, date_obj: date, language: Optional[str] = None, 
                   calendar_type: Optional[str] = None) -> str:
        """Format date according to Qatar preferences"""
        if language is None:
            language = self.current_language
            
        if calendar_type is None:
            calendar_type = self.settings.get('date_format', 'BUSINESS', 'hijri')
            
        if calendar_type == 'hijri':
            return self.format_hijri_date(date_obj, language)
        else:
            return self.format_gregorian_date(date_obj, language)
            
    def format_gregorian_date(self, date_obj: date, language: Optional[str] = None) -> str:
        """Format Gregorian date"""
        if language is None:
            language = self.current_language
            
        if language == 'ar':
            # Arabic format: 15 يناير 2024
            day = date_obj.day
            month = self.arabic_months[date_obj.month - 1]
            year = date_obj.year
            return f"{day} {month} {year}"
        else:
            # English format: 15 January 2024
            return date_obj.strftime("%d %B %Y")
            
    def format_hijri_date(self, date_obj: date, language: Optional[str] = None) -> str:
        """Format Hijri date (simplified conversion)"""
        if language is None:
            language = self.current_language
            
        # Simplified Hijri conversion (for demonstration)
        # In a real implementation, you would use a proper Hijri calendar library
        hijri_year = date_obj.year - 579  # Approximate conversion
        hijri_month = ((date_obj.month + 6) % 12) + 1  # Approximate
        hijri_day = date_obj.day
        
        if language == 'ar':
            month_name = self.hijri_months[hijri_month - 1]
            return f"{hijri_day} {month_name} {hijri_year} هـ"
        else:
            return f"{hijri_day}/{hijri_month}/{hijri_year} AH"
            
    def format_time(self, time_obj: datetime, language: Optional[str] = None) -> str:
        """Format time"""
        if language is None:
            language = self.current_language
            
        # Qatar uses 24-hour format
        return time_obj.strftime("%H:%M")
        
    def format_datetime(self, datetime_obj: datetime, language: Optional[str] = None) -> str:
        """Format date and time"""
        if language is None:
            language = self.current_language
            
        date_part = self.format_date(datetime_obj.date(), language)
        time_part = self.format_time(datetime_obj, language)
        
        if language == 'ar':
            return f"{date_part} - {time_part}"
        else:
            return f"{date_part} {time_part}"
            
    def format_number(self, number: float, decimals: int = 2, 
                     language: Optional[str] = None) -> str:
        """Format number with proper separators"""
        if language is None:
            language = self.current_language
            
        # Format with specified decimal places
        formatted = f"{number:,.{decimals}f}"
        
        # Arabic numbers use the same format as English for now
        # In a full implementation, you might want to use Arabic-Indic digits
        return formatted
        
    def get_currency_symbol(self, language: Optional[str] = None) -> str:
        """Get currency symbol"""
        if language is None:
            language = self.current_language
            
        return self.get_text('currency_symbol', language)
        
    def get_currency_code(self) -> str:
        """Get currency code"""
        return 'QAR'
        
    def is_rtl(self, language: Optional[str] = None) -> bool:
        """Check if language is right-to-left"""
        if language is None:
            language = self.current_language
            
        return language == 'ar'
        
    def get_text_direction(self, language: Optional[str] = None) -> str:
        """Get text direction for UI"""
        return 'rtl' if self.is_rtl(language) else 'ltr'
        
    def convert_to_arabic_digits(self, text: str) -> str:
        """Convert Western digits to Arabic-Indic digits"""
        arabic_digits = '٠١٢٣٤٥٦٧٨٩'
        western_digits = '0123456789'
        
        for western, arabic in zip(western_digits, arabic_digits):
            text = text.replace(western, arabic)
            
        return text
        
    def convert_to_western_digits(self, text: str) -> str:
        """Convert Arabic-Indic digits to Western digits"""
        arabic_digits = '٠١٢٣٤٥٦٧٨٩'
        western_digits = '0123456789'
        
        for arabic, western in zip(arabic_digits, western_digits):
            text = text.replace(arabic, western)
            
        return text
        
    def get_qatar_tax_info(self) -> Dict[str, Any]:
        """Get Qatar tax information"""
        return {
            'vat_rate': 0.0,  # Qatar currently has 0% VAT
            'tax_number_format': 'QAT-XXXXXXXXX',
            'commercial_registration_format': 'CR-XXXXXXX',
            'tax_authority': 'General Tax Authority - الهيئة العامة للضرائب',
            'tax_authority_website': 'https://www.tax.gov.qa',
            'e_invoicing_required': True,
            'qr_code_required': True
        }
        
    def validate_qatar_tax_number(self, tax_number: str) -> bool:
        """Validate Qatar tax number format"""
        # Simplified validation - in reality, this would be more complex
        import re
        pattern = r'^QAT-\d{9}$'
        return bool(re.match(pattern, tax_number))
        
    def validate_qatar_cr_number(self, cr_number: str) -> bool:
        """Validate Qatar commercial registration number"""
        import re
        pattern = r'^CR-\d{7}$'
        return bool(re.match(pattern, cr_number))

# Global localization instance
qatar_localization = QatarLocalization()
