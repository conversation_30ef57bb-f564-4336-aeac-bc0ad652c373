#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Product management for Qatar POS System
"""

import sqlite3
from datetime import datetime
from typing import List, Optional, Dict, Any
from dataclasses import asdict

from database.models import Product, Category, StockMovement
from database.database_manager import DatabaseManager
from core.auth import auth_manager
from utils.logger import get_logger

class ProductManager:
    """Product management class"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.logger = get_logger()
        
    def create_product(self, product: Product) -> bool:
        """Create a new product"""
        try:
            if not auth_manager.can_access_feature('product_management'):
                self.logger.warning("Unauthorized access to product creation")
                return False
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO products (
                        barcode, name_ar, name_en, description, category_id,
                        cost_price, selling_price, stock_quantity, min_stock_level, unit
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    product.barcode, product.name_ar, product.name_en,
                    product.description, product.category_id, product.cost_price,
                    product.selling_price, product.stock_quantity,
                    product.min_stock_level, product.unit
                ))
                
                product_id = cursor.lastrowid
                
                # Record initial stock movement if quantity > 0
                if product.stock_quantity > 0:
                    self._record_stock_movement(
                        cursor, product_id, 'in', product.stock_quantity,
                        'initial_stock', None, 'Initial stock entry'
                    )
                
                conn.commit()
                self.logger.info(f"Product created: {product.name_en} (ID: {product_id})")
                return True
                
        except sqlite3.IntegrityError as e:
            self.logger.error(f"Product creation failed - duplicate barcode: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error creating product: {e}")
            return False
            
    def update_product(self, product_id: int, updates: Dict[str, Any]) -> bool:
        """Update product information"""
        try:
            if not auth_manager.can_access_feature('product_management'):
                self.logger.warning("Unauthorized access to product update")
                return False
                
            # Build update query dynamically
            set_clauses = []
            values = []
            
            for field, value in updates.items():
                if field in ['name_ar', 'name_en', 'description', 'category_id',
                           'cost_price', 'selling_price', 'min_stock_level', 'unit', 'is_active']:
                    set_clauses.append(f"{field} = ?")
                    values.append(value)
                    
            if not set_clauses:
                return False
                
            set_clauses.append("updated_at = CURRENT_TIMESTAMP")
            values.append(product_id)
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                query = f"UPDATE products SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(query, values)
                
                conn.commit()
                
                if cursor.rowcount > 0:
                    self.logger.info(f"Product updated: ID {product_id}")
                    return True
                else:
                    self.logger.warning(f"Product not found: ID {product_id}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error updating product: {e}")
            return False
            
    def get_product_by_id(self, product_id: int) -> Optional[Dict[str, Any]]:
        """Get product by ID"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT p.*, c.name_ar as category_name_ar, c.name_en as category_name_en
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.id = ?
                """, (product_id,))
                
                row = cursor.fetchone()
                return dict(row) if row else None
                
        except Exception as e:
            self.logger.error(f"Error fetching product: {e}")
            return None
            
    def get_product_by_barcode(self, barcode: str) -> Optional[Dict[str, Any]]:
        """Get product by barcode"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT p.*, c.name_ar as category_name_ar, c.name_en as category_name_en
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.barcode = ? AND p.is_active = 1
                """, (barcode,))
                
                row = cursor.fetchone()
                return dict(row) if row else None
                
        except Exception as e:
            self.logger.error(f"Error fetching product by barcode: {e}")
            return None
            
    def search_products(self, query: str, category_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Search products by name or barcode"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                sql = """
                    SELECT p.*, c.name_ar as category_name_ar, c.name_en as category_name_en
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1 AND (
                        p.name_ar LIKE ? OR p.name_en LIKE ? OR p.barcode LIKE ?
                    )
                """
                params = [f"%{query}%", f"%{query}%", f"%{query}%"]
                
                if category_id:
                    sql += " AND p.category_id = ?"
                    params.append(category_id)
                    
                sql += " ORDER BY p.name_en"
                
                cursor.execute(sql, params)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Error searching products: {e}")
            return []
            
    def get_low_stock_products(self) -> List[Dict[str, Any]]:
        """Get products with low stock levels"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT p.*, c.name_ar as category_name_ar, c.name_en as category_name_en
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1 AND p.stock_quantity <= p.min_stock_level
                    ORDER BY p.stock_quantity ASC
                """)
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Error fetching low stock products: {e}")
            return []
            
    def update_stock(self, product_id: int, quantity_change: float, 
                    movement_type: str, reference_type: str = None, 
                    reference_id: int = None, notes: str = "") -> bool:
        """Update product stock quantity"""
        try:
            if not auth_manager.can_access_feature('inventory'):
                self.logger.warning("Unauthorized access to stock update")
                return False
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get current stock
                cursor.execute("SELECT stock_quantity FROM products WHERE id = ?", (product_id,))
                row = cursor.fetchone()
                
                if not row:
                    self.logger.error(f"Product not found: ID {product_id}")
                    return False
                    
                current_stock = row['stock_quantity']
                new_stock = current_stock + quantity_change
                
                if new_stock < 0:
                    self.logger.error(f"Insufficient stock for product ID {product_id}")
                    return False
                    
                # Update stock quantity
                cursor.execute("""
                    UPDATE products SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (new_stock, product_id))
                
                # Record stock movement
                self._record_stock_movement(
                    cursor, product_id, movement_type, abs(quantity_change),
                    reference_type, reference_id, notes
                )
                
                conn.commit()
                self.logger.info(f"Stock updated for product ID {product_id}: {current_stock} -> {new_stock}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating stock: {e}")
            return False
            
    def _record_stock_movement(self, cursor: sqlite3.Cursor, product_id: int,
                              movement_type: str, quantity: float,
                              reference_type: str = None, reference_id: int = None,
                              notes: str = ""):
        """Record stock movement in database"""
        user_id = auth_manager.get_current_user().user_id if auth_manager.is_authenticated() else 1
        
        cursor.execute("""
            INSERT INTO stock_movements (
                product_id, movement_type, quantity, reference_type,
                reference_id, notes, user_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (product_id, movement_type, quantity, reference_type, reference_id, notes, user_id))
        
    def get_stock_movements(self, product_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get stock movement history for a product"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT sm.*, u.full_name as user_name, p.name_en as product_name
                    FROM stock_movements sm
                    LEFT JOIN users u ON sm.user_id = u.id
                    LEFT JOIN products p ON sm.product_id = p.id
                    WHERE sm.product_id = ?
                    ORDER BY sm.created_at DESC
                    LIMIT ?
                """, (product_id, limit))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"Error fetching stock movements: {e}")
            return []

class CategoryManager:
    """Category management class"""

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.logger = get_logger()

    def create_category(self, name_ar: str, name_en: str, description: str = "") -> bool:
        """Create a new category"""
        try:
            if not auth_manager.can_access_feature('product_management'):
                return False

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO categories (name_ar, name_en, description)
                    VALUES (?, ?, ?)
                """, (name_ar, name_en, description))

                conn.commit()
                self.logger.info(f"Category created: {name_en}")
                return True

        except Exception as e:
            self.logger.error(f"Error creating category: {e}")
            return False

    def get_all_categories(self) -> List[Dict[str, Any]]:
        """Get all active categories"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM categories
                    WHERE is_active = 1
                    ORDER BY name_en
                """)

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"Error fetching categories: {e}")
            return []
