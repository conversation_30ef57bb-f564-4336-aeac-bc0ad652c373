#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Authentication and authorization system for Qatar POS
"""

import hashlib
import sqlite3
from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass

from database.models import User, UserRole
from database.database_manager import DatabaseManager
from utils.logger import get_logger

@dataclass
class AuthSession:
    """User authentication session"""
    user_id: int
    username: str
    full_name: str
    role: UserRole
    login_time: datetime
    
class AuthManager:
    """Authentication and authorization manager"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.logger = get_logger()
        self.current_session: Optional[AuthSession] = None
        
    def authenticate(self, username: str, password: str) -> bool:
        """Authenticate user with username and password"""
        try:
            password_hash = self._hash_password(password)
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id, username, full_name, role, is_active
                    FROM users 
                    WHERE username = ? AND password_hash = ? AND is_active = 1
                """, (username, password_hash))
                
                user_data = cursor.fetchone()
                
                if user_data:
                    # Update last login time
                    cursor.execute("""
                        UPDATE users SET last_login = CURRENT_TIMESTAMP 
                        WHERE id = ?
                    """, (user_data['id'],))
                    conn.commit()
                    
                    # Create session
                    self.current_session = AuthSession(
                        user_id=user_data['id'],
                        username=user_data['username'],
                        full_name=user_data['full_name'],
                        role=UserRole(user_data['role']),
                        login_time=datetime.now()
                    )
                    
                    self.logger.info(f"User {username} logged in successfully")
                    return True
                else:
                    self.logger.warning(f"Failed login attempt for username: {username}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return False
            
    def logout(self):
        """Logout current user"""
        if self.current_session:
            self.logger.info(f"User {self.current_session.username} logged out")
            self.current_session = None
            
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return self.current_session is not None
        
    def get_current_user(self) -> Optional[AuthSession]:
        """Get current authenticated user"""
        return self.current_session
        
    def has_permission(self, required_role: UserRole) -> bool:
        """Check if current user has required permission"""
        if not self.is_authenticated():
            return False
            
        user_role = self.current_session.role
        
        # Define role hierarchy
        role_hierarchy = {
            UserRole.MANAGER: 4,
            UserRole.ACCOUNTANT: 3,
            UserRole.INVENTORY: 2,
            UserRole.SELLER: 1
        }
        
        user_level = role_hierarchy.get(user_role, 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        return user_level >= required_level
        
    def can_access_feature(self, feature: str) -> bool:
        """Check if current user can access specific feature"""
        if not self.is_authenticated():
            return False
            
        user_role = self.current_session.role
        
        # Define feature permissions
        permissions = {
            'sales': [UserRole.MANAGER, UserRole.SELLER],
            'inventory': [UserRole.MANAGER, UserRole.INVENTORY, UserRole.SELLER],
            'customers': [UserRole.MANAGER, UserRole.SELLER, UserRole.ACCOUNTANT],
            'reports': [UserRole.MANAGER, UserRole.ACCOUNTANT],
            'user_management': [UserRole.MANAGER],
            'system_settings': [UserRole.MANAGER],
            'financial_reports': [UserRole.MANAGER, UserRole.ACCOUNTANT],
            'product_management': [UserRole.MANAGER, UserRole.INVENTORY],
            'invoice_management': [UserRole.MANAGER, UserRole.SELLER, UserRole.ACCOUNTANT]
        }
        
        allowed_roles = permissions.get(feature, [])
        return user_role in allowed_roles
        
    def create_user(self, username: str, password: str, full_name: str, 
                   role: UserRole, email: str = "", phone: str = "") -> bool:
        """Create new user (requires manager permission)"""
        if not self.has_permission(UserRole.MANAGER):
            self.logger.warning("Unauthorized attempt to create user")
            return False
            
        try:
            password_hash = self._hash_password(password)
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO users (username, password_hash, full_name, role, email, phone)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (username, password_hash, full_name, role.value, email, phone))
                
                conn.commit()
                
                self.logger.info(f"User {username} created successfully")
                return True
                
        except sqlite3.IntegrityError:
            self.logger.error(f"Username {username} already exists")
            return False
        except Exception as e:
            self.logger.error(f"Error creating user: {e}")
            return False
            
    def change_password(self, username: str, old_password: str, new_password: str) -> bool:
        """Change user password"""
        try:
            old_password_hash = self._hash_password(old_password)
            new_password_hash = self._hash_password(new_password)
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Verify old password
                cursor.execute("""
                    SELECT id FROM users 
                    WHERE username = ? AND password_hash = ?
                """, (username, old_password_hash))
                
                if not cursor.fetchone():
                    self.logger.warning(f"Invalid old password for user {username}")
                    return False
                    
                # Update password
                cursor.execute("""
                    UPDATE users SET password_hash = ? 
                    WHERE username = ?
                """, (new_password_hash, username))
                
                conn.commit()
                
                self.logger.info(f"Password changed for user {username}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error changing password: {e}")
            return False
            
    def get_all_users(self) -> list:
        """Get all users (requires manager permission)"""
        if not self.has_permission(UserRole.MANAGER):
            return []
            
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id, username, full_name, role, email, phone, 
                           is_active, created_at, last_login
                    FROM users
                    ORDER BY created_at DESC
                """)
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Error fetching users: {e}")
            return []
            
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()

# Global authentication manager instance
auth_manager = AuthManager()
