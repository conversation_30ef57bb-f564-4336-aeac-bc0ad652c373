#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test profit report functionality
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_profit_report():
    """Test profit report generation"""
    print("🧪 Testing Profit Report...")
    
    try:
        from core.auth import auth_manager
        from core.reports_manager import ReportsManager
        
        # Login as admin
        if auth_manager.authenticate('admin', 'admin123'):
            print("   ✅ Authentication successful")
            
            # Test profit report
            reports_manager = ReportsManager()
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            print(f"   📅 Testing period: {start_date} to {end_date}")
            
            report = reports_manager.get_profit_report(start_date, end_date)
            print("   ✅ Profit report generated successfully")
            
            print(f"   💰 Total Sales: {report.get('total_sales', 0):,.2f} QAR")
            print(f"   💵 Total Cost: {report.get('total_cost', 0):,.2f} QAR")
            print(f"   📈 Gross Profit: {report.get('gross_profit', 0):,.2f} QAR")
            print(f"   💸 Net Profit: {report.get('net_profit', 0):,.2f} QAR")
            
            auth_manager.logout()
            print("   ✅ Logout successful")
            
            return True
            
        else:
            print("   ❌ Authentication failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_profit_report()
    if success:
        print("\n🎉 Profit report test completed successfully!")
        print("✅ The profit report feature is now working")
        print("\n🚀 You can now access profit reports from:")
        print("   Main Menu → تقارير - Reports → تقرير الأرباح - Profit Report")
    else:
        print("\n❌ Profit report test failed")
    
    sys.exit(0 if success else 1)
