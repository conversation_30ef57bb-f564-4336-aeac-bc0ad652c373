# دليل التثبيت والنشر - Installation & Deployment Guide
## Qatar POS System - نظام نقاط البيع القطري

### 📋 جدول المحتويات - Table of Contents

1. [متطلبات النظام - System Requirements](#متطلبات-النظام---system-requirements)
2. [التثبيت السريع - Quick Installation](#التثبيت-السريع---quick-installation)
3. [التثبيت المتقدم - Advanced Installation](#التثبيت-المتقدم---advanced-installation)
4. [إعداد قاعدة البيانات - Database Setup](#إعداد-قاعدة-البيانات---database-setup)
5. [إعداد الأجهزة - Hardware Setup](#إعداد-الأجهزة---hardware-setup)
6. [النشر في بيئة الإنتاج - Production Deployment](#النشر-في-بيئة-الإنتاج---production-deployment)
7. [النسخ الاحتياطي والاستعادة - Backup & Restore](#النسخ-الاحتياطي-والاستعادة---backup--restore)
8. [استكشاف أخطاء التثبيت - Installation Troubleshooting](#استكشاف-أخطاء-التثبيت---installation-troubleshooting)

---

## متطلبات النظام - System Requirements

### الحد الأدنى - Minimum Requirements

| المكون | المتطلب |
|--------|---------|
| **نظام التشغيل** | Windows 10, macOS 10.14, Ubuntu 18.04 |
| **المعالج** | Intel Core i3 أو معادل |
| **الذاكرة** | 4 جيجابايت RAM |
| **مساحة القرص** | 2 جيجابايت مساحة فارغة |
| **الشاشة** | 1024x768 دقة على الأقل |
| **Python** | الإصدار 3.8 أو أحدث |

### المتطلبات الموصى بها - Recommended Requirements

| المكون | الموصى به |
|--------|-----------|
| **نظام التشغيل** | Windows 11, macOS 12+, Ubuntu 20.04+ |
| **المعالج** | Intel Core i5 أو أفضل |
| **الذاكرة** | 8 جيجابايت RAM |
| **مساحة القرص** | 5 جيجابايت مساحة فارغة |
| **الشاشة** | 1920x1080 أو أعلى |
| **الشبكة** | اتصال إنترنت للتحديثات |

### الأجهزة الاختيارية - Optional Hardware

- **طابعة حرارية**: 58mm أو 80mm
- **قارئ باركود**: USB أو Serial
- **درج نقد**: متصل بالطابعة
- **شاشة عميل**: لعرض الأسعار للعميل

---

## التثبيت السريع - Quick Installation

### Windows

#### الطريقة 1: استخدام الملف التنفيذي
```batch
# تحميل وتشغيل الملف التنفيذي
1. حمّل qatar-pos-installer.exe
2. انقر نقراً مزدوجاً على الملف
3. اتبع تعليمات المثبت
4. شغّل النظام من سطح المكتب
```

#### الطريقة 2: التثبيت اليدوي
```batch
# تحميل الكود المصدري
git clone https://github.com/your-repo/qatar-pos-system.git
cd qatar-pos-system

# تشغيل المثبت التلقائي
run_pos.bat
```

### macOS

```bash
# تثبيت Python إذا لم يكن موجوداً
brew install python3

# تحميل النظام
git clone https://github.com/your-repo/qatar-pos-system.git
cd qatar-pos-system

# تشغيل النظام
chmod +x run_pos.sh
./run_pos.sh
```

### Linux (Ubuntu/Debian)

```bash
# تحديث النظام
sudo apt update
sudo apt install python3 python3-pip python3-tk git

# تحميل النظام
git clone https://github.com/your-repo/qatar-pos-system.git
cd qatar-pos-system

# تثبيت المتطلبات
pip3 install -r requirements.txt

# تشغيل النظام
python3 main.py
```

---

## التثبيت المتقدم - Advanced Installation

### إعداد بيئة Python المعزولة

```bash
# إنشاء بيئة افتراضية
python -m venv qatar_pos_env

# تفعيل البيئة
# Windows:
qatar_pos_env\Scripts\activate
# macOS/Linux:
source qatar_pos_env/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### تثبيت المتطلبات يدوياً

```bash
# المكتبات الأساسية
pip install tkinter  # واجهة المستخدم
pip install Pillow   # معالجة الصور
pip install reportlab # إنشاء PDF
pip install qrcode   # إنشاء رموز QR
pip install pyserial # التواصل مع الأجهزة

# مكتبات اختيارية
pip install python-bidi  # دعم النصوص العربية
pip install arabic-reshaper  # تشكيل النصوص العربية
```

### إعداد الخطوط العربية

```bash
# إنشاء مجلد الخطوط
mkdir -p assets/fonts

# تحميل خط عربي (مثال)
# ضع ملف الخط في assets/fonts/NotoSansArabic-Regular.ttf
```

---

## إعداد قاعدة البيانات - Database Setup

### الإعداد التلقائي
النظام ينشئ قاعدة البيانات تلقائياً عند أول تشغيل.

### الإعداد اليدوي

```bash
# إنشاء مجلد قاعدة البيانات
mkdir -p database

# تشغيل سكريبت إنشاء قاعدة البيانات
python -c "
from database.database_manager import DatabaseManager
db = DatabaseManager()
db.initialize_database()
print('Database initialized successfully')
"
```

### استيراد بيانات تجريبية

```bash
# تشغيل سكريبت البيانات التجريبية
python scripts/import_sample_data.py
```

### نسخ قاعدة بيانات موجودة

```bash
# نسخ قاعدة بيانات من نظام آخر
cp /path/to/existing/pos_system.db database/pos_system.db

# أو استعادة من نسخة احتياطية
cp backup/pos_backup_20240115.db database/pos_system.db
```

---

## إعداد الأجهزة - Hardware Setup

### الطابعة الحرارية

#### إعداد Windows
1. **توصيل الطابعة**:
   - وصّل الطابعة بمنفذ USB أو Serial
   - ثبّت تعريف الطابعة من الشركة المصنعة

2. **تحديد المنفذ**:
   ```
   - USB: عادة يظهر كـ COM port جديد
   - Serial: COM1, COM2, إلخ
   - Network: عنوان IP للطابعة
   ```

3. **اختبار الطابعة**:
   ```bash
   # من إعدادات النظام
   النظام → الإعدادات → الأجهزة → اختبار الطابعة
   ```

#### إعداد Linux
```bash
# تثبيت دعم الطابعات
sudo apt install cups cups-client

# إضافة المستخدم لمجموعة الطابعات
sudo usermod -a -G lp $USER

# إعادة تسجيل الدخول أو إعادة التشغيل
```

### قارئ الباركود

#### إعداد USB Scanner
```
1. وصّل القارئ بمنفذ USB
2. القارئ سيعمل كلوحة مفاتيح (Keyboard Wedge)
3. لا حاجة لتعريفات إضافية
4. اختبر بمسح باركود في أي محرر نصوص
```

#### إعداد Serial Scanner
```
1. وصّل القارئ بمنفذ Serial (COM)
2. حدد المنفذ في إعدادات النظام
3. اختبر من إعدادات الأجهزة
```

### درج النقد

```
1. وصّل الدرج بالطابعة الحرارية (RJ11/RJ12)
2. أو وصّل مباشرة بالكمبيوتر (Serial)
3. فعّل الدرج من إعدادات النظام
4. اختبر فتح الدرج من الإعدادات
```

---

## النشر في بيئة الإنتاج - Production Deployment

### إعداد خادم مخصص

#### متطلبات الخادم
```
- Windows Server 2019+ أو Ubuntu Server 20.04+
- 8 جيجابايت RAM على الأقل
- 50 جيجابايت مساحة قرص
- اتصال شبكة مستقر
```

#### تثبيت النظام على الخادم

```bash
# إنشاء مستخدم مخصص
sudo useradd -m -s /bin/bash qatarpos
sudo passwd qatarpos

# تبديل للمستخدم الجديد
sudo su - qatarpos

# تحميل وتثبيت النظام
git clone https://github.com/your-repo/qatar-pos-system.git
cd qatar-pos-system
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### إعداد النظام كخدمة

#### Windows Service
```batch
# إنشاء ملف خدمة Windows
# استخدم NSSM (Non-Sucking Service Manager)

nssm install QatarPOS
nssm set QatarPOS Application "C:\path\to\python.exe"
nssm set QatarPOS AppParameters "C:\path\to\main.py"
nssm set QatarPOS AppDirectory "C:\path\to\qatar-pos-system"
nssm start QatarPOS
```

#### Linux Systemd Service
```bash
# إنشاء ملف الخدمة
sudo nano /etc/systemd/system/qatarpos.service

# محتوى الملف:
[Unit]
Description=Qatar POS System
After=network.target

[Service]
Type=simple
User=qatarpos
WorkingDirectory=/home/<USER>/qatar-pos-system
Environment=PATH=/home/<USER>/qatar-pos-system/venv/bin
ExecStart=/home/<USER>/qatar-pos-system/venv/bin/python main.py
Restart=always

[Install]
WantedBy=multi-user.target

# تفعيل الخدمة
sudo systemctl enable qatarpos
sudo systemctl start qatarpos
```

### إعداد الشبكة

#### فتح المنافذ المطلوبة
```bash
# Windows Firewall
netsh advfirewall firewall add rule name="Qatar POS" dir=in action=allow protocol=TCP localport=8080

# Linux UFW
sudo ufw allow 8080/tcp
sudo ufw enable
```

#### إعداد الوصول عن بُعد
```bash
# تثبيت VNC Server للوصول الرسومي
sudo apt install tightvncserver

# تشغيل VNC
vncserver :1 -geometry 1920x1080 -depth 24
```

---

## النسخ الاحتياطي والاستعادة - Backup & Restore

### النسخ الاحتياطي التلقائي

#### إعداد النسخ التلقائي
```bash
# إنشاء سكريبت النسخ الاحتياطي
nano scripts/auto_backup.sh

#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="database/backups"
mkdir -p $BACKUP_DIR

# نسخ قاعدة البيانات
cp database/pos_system.db $BACKUP_DIR/pos_backup_$DATE.db

# نسخ الإعدادات
cp config/config.ini $BACKUP_DIR/config_backup_$DATE.ini

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.db" -mtime +30 -delete

echo "Backup completed: $DATE"
```

#### جدولة النسخ الاحتياطي

**Windows (Task Scheduler):**
```batch
# إنشاء مهمة مجدولة
schtasks /create /tn "Qatar POS Backup" /tr "C:\path\to\backup.bat" /sc daily /st 02:00
```

**Linux (Cron):**
```bash
# إضافة مهمة cron
crontab -e

# إضافة السطر التالي للنسخ اليومي في الساعة 2 صباحاً
0 2 * * * /home/<USER>/qatar-pos-system/scripts/auto_backup.sh
```

### النسخ الاحتياطي اليدوي

```bash
# نسخ احتياطي كامل
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p backups/full_backup_$DATE

# نسخ جميع الملفات المهمة
cp -r database backups/full_backup_$DATE/
cp -r config backups/full_backup_$DATE/
cp -r assets backups/full_backup_$DATE/
cp -r logs backups/full_backup_$DATE/

# ضغط النسخة الاحتياطية
tar -czf backups/qatar_pos_backup_$DATE.tar.gz backups/full_backup_$DATE/
```

### الاستعادة من نسخة احتياطية

```bash
# إيقاف النظام
sudo systemctl stop qatarpos  # Linux
# أو إغلاق التطبيق يدوياً

# استعادة قاعدة البيانات
cp backups/pos_backup_20240115_120000.db database/pos_system.db

# استعادة الإعدادات
cp backups/config_backup_20240115_120000.ini config/config.ini

# إعادة تشغيل النظام
sudo systemctl start qatarpos  # Linux
```

---

## استكشاف أخطاء التثبيت - Installation Troubleshooting

### مشاكل Python

#### خطأ: Python not found
```bash
# Windows: تثبيت Python من python.org
# تأكد من إضافة Python للـ PATH

# macOS: تثبيت عبر Homebrew
brew install python3

# Linux: تثبيت عبر مدير الحزم
sudo apt install python3 python3-pip
```

#### خطأ: pip not found
```bash
# تثبيت pip
python -m ensurepip --upgrade

# أو تحميل get-pip.py
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
python get-pip.py
```

### مشاكل المكتبات

#### خطأ: tkinter not found
```bash
# Linux: تثبيت tkinter
sudo apt install python3-tk

# macOS: عادة مثبت مع Python
# Windows: مثبت مع Python
```

#### خطأ: Pillow installation failed
```bash
# تثبيت متطلبات Pillow
# Linux:
sudo apt install libjpeg-dev zlib1g-dev

# macOS:
brew install libjpeg zlib

# ثم إعادة تثبيت Pillow
pip install --upgrade Pillow
```

### مشاكل قاعدة البيانات

#### خطأ: Database locked
```bash
# إغلاق جميع اتصالات قاعدة البيانات
pkill -f "python.*main.py"

# حذف ملف القفل إذا وجد
rm database/pos_system.db-wal
rm database/pos_system.db-shm

# إعادة تشغيل النظام
python main.py
```

#### خطأ: Database corrupted
```bash
# استعادة من نسخة احتياطية
cp database/backups/latest_backup.db database/pos_system.db

# أو إنشاء قاعدة بيانات جديدة
rm database/pos_system.db
python main.py  # سينشئ قاعدة بيانات جديدة
```

### مشاكل الأجهزة

#### الطابعة لا تعمل
```bash
# تحقق من التوصيل
lsusb  # Linux
# أو Device Manager في Windows

# تحقق من المنفذ
ls /dev/ttyUSB*  # Linux
ls /dev/ttyACM*  # Linux

# اختبار الطابعة
echo "Test" > /dev/ttyUSB0  # Linux
```

#### قارئ الباركود لا يعمل
```bash
# تحقق من التعرف على الجهاز
lsusb | grep -i barcode  # Linux

# اختبار في محرر نصوص
# امسح باركود ويجب أن يظهر النص
```

### مشاكل الأداء

#### النظام بطيء
```bash
# تحقق من استخدام الذاكرة
free -h  # Linux
# أو Task Manager في Windows

# تحقق من مساحة القرص
df -h  # Linux
# أو File Explorer في Windows

# تنظيف ملفات السجل
find logs/ -name "*.log" -mtime +7 -delete
```

### الحصول على المساعدة

#### جمع معلومات النظام
```bash
# إنشاء تقرير النظام
python scripts/system_info.py > system_report.txt

# أو يدوياً:
echo "Python Version:" > system_report.txt
python --version >> system_report.txt
echo "OS Info:" >> system_report.txt
uname -a >> system_report.txt  # Linux/macOS
# أو systeminfo في Windows
```

#### ملفات السجل
```bash
# مواقع ملفات السجل
logs/pos_system.log      # السجل الرئيسي
logs/error.log           # سجل الأخطاء
logs/database.log        # سجل قاعدة البيانات
logs/hardware.log        # سجل الأجهزة
```

#### الاتصال بالدعم الفني
عند الاتصال بالدعم، يرجى تضمين:
- إصدار النظام
- نظام التشغيل
- رسالة الخطأ الكاملة
- ملفات السجل ذات الصلة
- خطوات إعادة إنتاج المشكلة

**معلومات الاتصال:**
- البريد الإلكتروني: <EMAIL>
- الهاتف: +974 XXXX XXXX
- الموقع: https://docs.qatarpos.com

---

**نظام نقاط البيع القطري** - تثبيت موثوق وآمن
**Qatar POS System** - Reliable and Secure Installation
