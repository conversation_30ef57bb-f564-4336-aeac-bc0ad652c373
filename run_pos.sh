#!/bin/bash

echo ""
echo "========================================"
echo "  Qatar POS System - نظام نقاط البيع القطري"
echo "========================================"
echo ""
echo "Starting Qatar POS System..."
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed"
    echo "Please install Python 3.8 or higher"
    exit 1
fi

# Check Python version
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "ERROR: Python $required_version or higher is required"
    echo "Current version: $python_version"
    exit 1
fi

# Check if required packages are installed
echo "Checking dependencies..."
python3 -c "import tkinter, sqlite3" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing required packages..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to install dependencies"
        exit 1
    fi
fi

# Make sure we're in the right directory
cd "$(dirname "$0")"

# Run the application
echo ""
echo "Launching Qatar POS System..."
echo ""
python3 main.py

if [ $? -ne 0 ]; then
    echo ""
    echo "ERROR: Application failed to start"
    read -p "Press Enter to continue..."
fi

echo ""
echo "Application closed."
