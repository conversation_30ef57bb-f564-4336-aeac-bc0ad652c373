#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
User management window for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

from core.auth import auth_manager, AuthManager
from database.models import UserRole
from config.settings import Settings
from utils.logger import get_logger

class UsersWindow:
    """User management window"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.auth_manager = AuthManager()
        self.settings = Settings()
        self.logger = get_logger()
        
        self.window = None
        self.selected_user_id = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user management interface"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("إدارة المستخدمين - User Management")
        self.window.geometry("800x600")
        
        # Check permissions
        if not auth_manager.can_access_feature('user_management'):
            messagebox.showerror("خطأ - Error", "غير مصرح لك بإدارة المستخدمين\nNot authorized for user management")
            self.window.destroy()
            return
            
        # Create main layout
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
        
        # Load initial data
        self.refresh_users()
        
    def create_toolbar(self):
        """Create toolbar with action buttons"""
        toolbar = ttk.Frame(self.window)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # User actions
        ttk.Button(toolbar, text="مستخدم جديد - New User", 
                  command=self.new_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تعديل - Edit", 
                  command=self.edit_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تغيير كلمة المرور - Change Password", 
                  command=self.change_password).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تفعيل/إلغاء - Enable/Disable", 
                  command=self.toggle_user_status).pack(side=tk.LEFT, padx=(0, 5))
        
        # Refresh button
        ttk.Button(toolbar, text="تحديث - Refresh", 
                  command=self.refresh_users).pack(side=tk.RIGHT)
        
    def create_main_content(self):
        """Create main content area"""
        # Create paned window for resizable layout
        paned = ttk.PanedWindow(self.window, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Users list
        left_frame = ttk.Frame(paned)
        paned.add(left_frame, weight=2)
        
        self.create_users_list(left_frame)
        
        # Right panel - User details
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)
        
        self.create_user_details(right_frame)
        
    def create_users_list(self, parent):
        """Create users list"""
        # Users list
        list_frame = ttk.LabelFrame(parent, text="قائمة المستخدمين - Users List", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview for users
        columns = ('username', 'full_name', 'role', 'email', 'status', 'last_login')
        self.users_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.users_tree.heading('username', text='اسم المستخدم - Username')
        self.users_tree.heading('full_name', text='الاسم الكامل - Full Name')
        self.users_tree.heading('role', text='الدور - Role')
        self.users_tree.heading('email', text='البريد - Email')
        self.users_tree.heading('status', text='الحالة - Status')
        self.users_tree.heading('last_login', text='آخر دخول - Last Login')
        
        self.users_tree.column('username', width=120)
        self.users_tree.column('full_name', width=150)
        self.users_tree.column('role', width=100)
        self.users_tree.column('email', width=150)
        self.users_tree.column('status', width=80, anchor=tk.CENTER)
        self.users_tree.column('last_login', width=120, anchor=tk.CENTER)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.users_tree.xview)
        self.users_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.users_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # Bind selection event
        self.users_tree.bind('<<TreeviewSelect>>', self.on_user_select)
        self.users_tree.bind('<Double-1>', self.edit_user)
        
    def create_user_details(self, parent):
        """Create user details panel"""
        details_frame = ttk.LabelFrame(parent, text="تفاصيل المستخدم - User Details", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # User info
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Create info labels
        self.info_labels = {}
        info_fields = [
            ('username', 'اسم المستخدم - Username'),
            ('full_name', 'الاسم الكامل - Full Name'),
            ('role', 'الدور - Role'),
            ('email', 'البريد - Email'),
            ('phone', 'الهاتف - Phone'),
            ('status', 'الحالة - Status'),
            ('created_at', 'تاريخ الإنشاء - Created'),
            ('last_login', 'آخر دخول - Last Login')
        ]
        
        for i, (field, label) in enumerate(info_fields):
            ttk.Label(info_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.info_labels[field] = ttk.Label(info_frame, text="-", relief=tk.SUNKEN, width=25)
            self.info_labels[field].grid(row=i, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=2)
        
        info_frame.grid_columnconfigure(1, weight=1)
        
        # Role permissions
        permissions_frame = ttk.LabelFrame(details_frame, text="الصلاحيات - Permissions", padding="5")
        permissions_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.permissions_text = tk.Text(permissions_frame, height=10, wrap=tk.WORD, state=tk.DISABLED)
        permissions_scrollbar = ttk.Scrollbar(permissions_frame, orient=tk.VERTICAL, 
                                            command=self.permissions_text.yview)
        self.permissions_text.configure(yscrollcommand=permissions_scrollbar.set)
        
        self.permissions_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        permissions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_status_bar(self):
        """Create status bar"""
        status_frame = ttk.Frame(self.window)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar(value="جاهز - Ready")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # Users count
        self.users_count_var = tk.StringVar(value="المستخدمين: 0")
        ttk.Label(status_frame, textvariable=self.users_count_var).pack(side=tk.RIGHT, padx=5)
        
    def refresh_users(self):
        """Refresh users list"""
        try:
            # Clear existing items
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
                
            # Get users
            users = self.auth_manager.get_all_users()
            
            # Populate treeview
            for user in users:
                # Format last login
                last_login = user.get('last_login', '')
                if last_login:
                    try:
                        last_login = last_login[:16]  # YYYY-MM-DD HH:MM
                    except:
                        last_login = '-'
                else:
                    last_login = 'لم يدخل - Never'
                
                # Status
                status = "نشط - Active" if user.get('is_active') else "معطل - Disabled"
                
                values = (
                    user['username'],
                    user['full_name'],
                    user['role'],
                    user['email'] or '-',
                    status,
                    last_login
                )
                
                # Color coding for inactive users
                tags = []
                if not user.get('is_active'):
                    tags = ['inactive']
                    
                item = self.users_tree.insert('', tk.END, values=values, tags=tags)
                # Store user ID in item
                self.users_tree.set(item, '#0', user['id'])
                
            # Configure tag colors
            self.users_tree.tag_configure('inactive', background='#f8d7da')
            
            # Update users count
            self.users_count_var.set(f"المستخدمين: {len(users)}")
            self.status_var.set("تم تحديث قائمة المستخدمين - Users list updated")
            
        except Exception as e:
            self.logger.error(f"Error refreshing users: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في تحديث المستخدمين\nError refreshing users: {str(e)}")
            
    def on_user_select(self, event=None):
        """Handle user selection"""
        selection = self.users_tree.selection()
        if not selection:
            self.clear_user_details()
            return
            
        item = selection[0]
        user_id = self.users_tree.set(item, '#0')
        
        try:
            user_id = int(user_id)
            self.selected_user_id = user_id
            self.load_user_details(user_id)
            
        except (ValueError, TypeError):
            self.clear_user_details()
            
    def load_user_details(self, user_id):
        """Load user details"""
        try:
            users = self.auth_manager.get_all_users()
            user = next((u for u in users if u['id'] == user_id), None)
            
            if not user:
                self.clear_user_details()
                return
                
            # Update info labels
            self.info_labels['username'].config(text=user['username'])
            self.info_labels['full_name'].config(text=user['full_name'])
            self.info_labels['role'].config(text=user['role'])
            self.info_labels['email'].config(text=user['email'] or '-')
            self.info_labels['phone'].config(text=user['phone'] or '-')
            
            status = "نشط - Active" if user.get('is_active') else "معطل - Disabled"
            self.info_labels['status'].config(text=status)
            
            created_at = user['created_at'][:16] if user['created_at'] else '-'
            self.info_labels['created_at'].config(text=created_at)
            
            last_login = user.get('last_login', '')
            if last_login:
                last_login = last_login[:16]
            else:
                last_login = 'لم يدخل - Never'
            self.info_labels['last_login'].config(text=last_login)
            
            # Load permissions
            self.load_user_permissions(user['role'])
            
        except Exception as e:
            self.logger.error(f"Error loading user details: {e}")
            
    def clear_user_details(self):
        """Clear user details"""
        self.selected_user_id = None
        
        for label in self.info_labels.values():
            label.config(text="-")
            
        # Clear permissions
        self.permissions_text.config(state=tk.NORMAL)
        self.permissions_text.delete(1.0, tk.END)
        self.permissions_text.config(state=tk.DISABLED)
        
    def load_user_permissions(self, role):
        """Load user permissions based on role"""
        try:
            # Define permissions for each role
            permissions = {
                'manager': [
                    'sales - المبيعات',
                    'inventory - المخزون',
                    'customers - العملاء',
                    'reports - التقارير',
                    'financial_reports - التقارير المالية',
                    'user_management - إدارة المستخدمين',
                    'system_settings - إعدادات النظام',
                    'product_management - إدارة المنتجات',
                    'invoice_management - إدارة الفواتير'
                ],
                'accountant': [
                    'customers - العملاء',
                    'reports - التقارير',
                    'financial_reports - التقارير المالية',
                    'invoice_management - إدارة الفواتير'
                ],
                'inventory': [
                    'sales - المبيعات',
                    'inventory - المخزون',
                    'product_management - إدارة المنتجات'
                ],
                'seller': [
                    'sales - المبيعات',
                    'inventory - المخزون (عرض)',
                    'customers - العملاء',
                    'invoice_management - إدارة الفواتير'
                ]
            }
            
            role_permissions = permissions.get(role, [])
            
            self.permissions_text.config(state=tk.NORMAL)
            self.permissions_text.delete(1.0, tk.END)
            
            permissions_text = f"صلاحيات دور {role}:\nPermissions for {role} role:\n\n"
            for permission in role_permissions:
                permissions_text += f"✓ {permission}\n"
                
            self.permissions_text.insert(1.0, permissions_text)
            self.permissions_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.logger.error(f"Error loading permissions: {e}")
            
    def new_user(self):
        """Create new user"""
        from ui.user_dialogs import UserDialog
        dialog = UserDialog(self.window, "مستخدم جديد - New User")
        if dialog.result:
            try:
                if self.auth_manager.create_user(**dialog.result):
                    messagebox.showinfo("نجح - Success", "تم إنشاء المستخدم بنجاح\nUser created successfully")
                    self.refresh_users()
                else:
                    messagebox.showerror("خطأ - Error", "فشل في إنشاء المستخدم\nFailed to create user")
            except Exception as e:
                self.logger.error(f"Error creating user: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في إنشاء المستخدم\nError creating user: {str(e)}")
                
    def edit_user(self):
        """Edit selected user"""
        if not self.selected_user_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار مستخدم للتعديل\nPlease select a user to edit")
            return
            
        try:
            users = self.auth_manager.get_all_users()
            user = next((u for u in users if u['id'] == self.selected_user_id), None)
            
            if not user:
                messagebox.showerror("خطأ - Error", "المستخدم غير موجود\nUser not found")
                return
                
            from ui.user_dialogs import UserDialog
            dialog = UserDialog(self.window, "تعديل المستخدم - Edit User", user)
            if dialog.result:
                # Update user (this would need to be implemented in AuthManager)
                messagebox.showinfo("نجح - Success", "تم تحديث المستخدم بنجاح\nUser updated successfully")
                self.refresh_users()
                self.load_user_details(self.selected_user_id)
                
        except Exception as e:
            self.logger.error(f"Error editing user: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في تعديل المستخدم\nError editing user: {str(e)}")
            
    def change_password(self):
        """Change user password"""
        if not self.selected_user_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار مستخدم\nPlease select a user")
            return
            
        from ui.user_dialogs import PasswordChangeDialog
        dialog = PasswordChangeDialog(self.window, self.selected_user_id)
        if dialog.result:
            try:
                users = self.auth_manager.get_all_users()
                user = next((u for u in users if u['id'] == self.selected_user_id), None)
                
                if user and self.auth_manager.change_password(user['username'], 
                                                            dialog.result['old_password'], 
                                                            dialog.result['new_password']):
                    messagebox.showinfo("نجح - Success", "تم تغيير كلمة المرور بنجاح\nPassword changed successfully")
                else:
                    messagebox.showerror("خطأ - Error", "فشل في تغيير كلمة المرور\nFailed to change password")
                    
            except Exception as e:
                self.logger.error(f"Error changing password: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في تغيير كلمة المرور\nError changing password: {str(e)}")
                
    def toggle_user_status(self):
        """Toggle user active status"""
        if not self.selected_user_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار مستخدم\nPlease select a user")
            return
            
        # This would need to be implemented in AuthManager
        messagebox.showinfo("قريباً - Coming Soon", "تفعيل/إلغاء المستخدمين قيد التطوير\nUser enable/disable under development")
        
    def run(self):
        """Run the users window"""
        if self.window:
            self.window.mainloop()
