#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
User management dialogs for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import re

from database.models import UserRole
from config.settings import Settings
from utils.logger import get_logger

class UserDialog:
    """User creation/editing dialog"""
    
    def __init__(self, parent, title, user_data=None):
        self.parent = parent
        self.user_data = user_data or {}
        self.settings = Settings()
        self.logger = get_logger()
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.setup_ui()
        self.load_data()
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
    def setup_ui(self):
        """Setup dialog UI"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # User info section
        info_frame = ttk.LabelFrame(main_frame, text="معلومات المستخدم - User Information", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Username
        ttk.Label(info_frame, text="اسم المستخدم - Username: *").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.username_var = tk.StringVar()
        username_entry = ttk.Entry(info_frame, textvariable=self.username_var, width=30)
        username_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        username_entry.bind('<KeyRelease>', self.validate_username)
        
        # Full name
        ttk.Label(info_frame, text="الاسم الكامل - Full Name: *").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.full_name_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.full_name_var, width=30).grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Email
        ttk.Label(info_frame, text="البريد الإلكتروني - Email:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar()
        email_entry = ttk.Entry(info_frame, textvariable=self.email_var, width=30)
        email_entry.grid(row=2, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        email_entry.bind('<KeyRelease>', self.validate_email)
        
        # Phone
        ttk.Label(info_frame, text="الهاتف - Phone:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.phone_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.phone_var, width=30).grid(row=3, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Role
        ttk.Label(info_frame, text="الدور - Role: *").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.role_var = tk.StringVar()
        role_combo = ttk.Combobox(info_frame, textvariable=self.role_var, state="readonly", width=27)
        role_combo['values'] = [
            ("manager", "مدير - Manager"),
            ("accountant", "محاسب - Accountant"),
            ("inventory", "مخزون - Inventory"),
            ("seller", "بائع - Seller")
        ]
        role_combo.grid(row=4, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        info_frame.grid_columnconfigure(1, weight=1)
        
        # Password section (only for new users)
        if not self.user_data:
            password_frame = ttk.LabelFrame(main_frame, text="كلمة المرور - Password", padding="10")
            password_frame.pack(fill=tk.X, pady=(0, 10))
            
            # Password
            ttk.Label(password_frame, text="كلمة المرور - Password: *").grid(row=0, column=0, sticky=tk.W, pady=5)
            self.password_var = tk.StringVar()
            password_entry = ttk.Entry(password_frame, textvariable=self.password_var, show="*", width=30)
            password_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
            password_entry.bind('<KeyRelease>', self.validate_password)
            
            # Confirm password
            ttk.Label(password_frame, text="تأكيد كلمة المرور - Confirm Password: *").grid(row=1, column=0, sticky=tk.W, pady=5)
            self.confirm_password_var = tk.StringVar()
            confirm_entry = ttk.Entry(password_frame, textvariable=self.confirm_password_var, show="*", width=30)
            confirm_entry.grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
            confirm_entry.bind('<KeyRelease>', self.validate_confirm_password)
            
            password_frame.grid_columnconfigure(1, weight=1)
        else:
            # For editing, we don't show password fields
            self.password_var = None
            self.confirm_password_var = None
            
        # Validation labels
        self.validation_frame = ttk.Frame(main_frame)
        self.validation_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.username_validation_label = ttk.Label(self.validation_frame, text="", foreground="red")
        self.username_validation_label.pack(anchor=tk.W)
        
        self.email_validation_label = ttk.Label(self.validation_frame, text="", foreground="red")
        self.email_validation_label.pack(anchor=tk.W)
        
        self.password_validation_label = ttk.Label(self.validation_frame, text="", foreground="red")
        self.password_validation_label.pack(anchor=tk.W)
        
        self.confirm_password_validation_label = ttk.Label(self.validation_frame, text="", foreground="red")
        self.confirm_password_validation_label.pack(anchor=tk.W)
        
        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="حفظ - Save", command=self.save_user).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء - Cancel", command=self.cancel).pack(side=tk.RIGHT)
        
    def validate_username(self, event=None):
        """Validate username format"""
        username = self.username_var.get()
        if username:
            if not re.match(r'^[a-zA-Z0-9_]{3,20}$', username):
                self.username_validation_label.config(text="اسم المستخدم يجب أن يكون 3-20 حرف (أحرف وأرقام فقط) - Username must be 3-20 characters (letters and numbers only)")
            else:
                self.username_validation_label.config(text="")
        else:
            self.username_validation_label.config(text="")
            
    def validate_email(self, event=None):
        """Validate email format"""
        email = self.email_var.get()
        if email:
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                self.email_validation_label.config(text="تنسيق البريد الإلكتروني غير صحيح - Invalid email format")
            else:
                self.email_validation_label.config(text="")
        else:
            self.email_validation_label.config(text="")
            
    def validate_password(self, event=None):
        """Validate password strength"""
        if not self.password_var:
            return
            
        password = self.password_var.get()
        if password:
            if len(password) < 6:
                self.password_validation_label.config(text="كلمة المرور يجب أن تكون 6 أحرف على الأقل - Password must be at least 6 characters")
            else:
                self.password_validation_label.config(text="")
        else:
            self.password_validation_label.config(text="")
            
    def validate_confirm_password(self, event=None):
        """Validate password confirmation"""
        if not self.password_var or not self.confirm_password_var:
            return
            
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()
        
        if confirm_password:
            if password != confirm_password:
                self.confirm_password_validation_label.config(text="كلمات المرور غير متطابقة - Passwords do not match")
            else:
                self.confirm_password_validation_label.config(text="")
        else:
            self.confirm_password_validation_label.config(text="")
            
    def load_data(self):
        """Load existing user data if editing"""
        if not self.user_data:
            return
            
        self.username_var.set(self.user_data.get('username', ''))
        self.full_name_var.set(self.user_data.get('full_name', ''))
        self.email_var.set(self.user_data.get('email', ''))
        self.phone_var.set(self.user_data.get('phone', ''))
        self.role_var.set(self.user_data.get('role', 'seller'))
        
    def save_user(self):
        """Save user data"""
        try:
            # Validate required fields
            if not self.username_var.get().strip():
                messagebox.showerror("خطأ - Error", "يرجى إدخال اسم المستخدم\nPlease enter username")
                return
                
            if not self.full_name_var.get().strip():
                messagebox.showerror("خطأ - Error", "يرجى إدخال الاسم الكامل\nPlease enter full name")
                return
                
            if not self.role_var.get():
                messagebox.showerror("خطأ - Error", "يرجى اختيار الدور\nPlease select role")
                return
                
            # For new users, validate password
            if not self.user_data:
                if not self.password_var.get():
                    messagebox.showerror("خطأ - Error", "يرجى إدخال كلمة المرور\nPlease enter password")
                    return
                    
                if self.password_var.get() != self.confirm_password_var.get():
                    messagebox.showerror("خطأ - Error", "كلمات المرور غير متطابقة\nPasswords do not match")
                    return
                    
            # Check validation errors
            if (self.username_validation_label.cget("text") or 
                self.email_validation_label.cget("text") or 
                self.password_validation_label.cget("text") or 
                self.confirm_password_validation_label.cget("text")):
                messagebox.showerror("خطأ - Error", "يرجى تصحيح الأخطاء أولاً\nPlease fix validation errors first")
                return
                
            # Prepare result data
            self.result = {
                'username': self.username_var.get().strip(),
                'full_name': self.full_name_var.get().strip(),
                'email': self.email_var.get().strip() or None,
                'phone': self.phone_var.get().strip() or None,
                'role': UserRole(self.role_var.get())
            }
            
            # Add password for new users
            if not self.user_data and self.password_var:
                self.result['password'] = self.password_var.get()
                
            self.dialog.destroy()
            
        except Exception as e:
            self.logger.error(f"Error saving user: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في حفظ البيانات\nError saving data: {str(e)}")
            
    def cancel(self):
        """Cancel dialog"""
        self.result = None
        self.dialog.destroy()

class PasswordChangeDialog:
    """Password change dialog"""
    
    def __init__(self, parent, user_id):
        self.parent = parent
        self.user_id = user_id
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("تغيير كلمة المرور - Change Password")
        self.dialog.geometry("400x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.setup_ui()
        
        # Wait for dialog to close
        self.dialog.wait_window()
        
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
    def setup_ui(self):
        """Setup dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Old password
        ttk.Label(main_frame, text="كلمة المرور الحالية - Current Password:").pack(anchor=tk.W, pady=(0, 5))
        self.old_password_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.old_password_var, show="*", width=30).pack(fill=tk.X, pady=(0, 15))
        
        # New password
        ttk.Label(main_frame, text="كلمة المرور الجديدة - New Password:").pack(anchor=tk.W, pady=(0, 5))
        self.new_password_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.new_password_var, show="*", width=30).pack(fill=tk.X, pady=(0, 15))
        
        # Confirm new password
        ttk.Label(main_frame, text="تأكيد كلمة المرور الجديدة - Confirm New Password:").pack(anchor=tk.W, pady=(0, 5))
        self.confirm_new_password_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.confirm_new_password_var, show="*", width=30).pack(fill=tk.X, pady=(0, 15))
        
        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="تغيير - Change", command=self.change_password).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء - Cancel", command=self.cancel).pack(side=tk.RIGHT)
        
    def change_password(self):
        """Change password"""
        old_password = self.old_password_var.get()
        new_password = self.new_password_var.get()
        confirm_password = self.confirm_new_password_var.get()
        
        if not old_password:
            messagebox.showerror("خطأ - Error", "يرجى إدخال كلمة المرور الحالية\nPlease enter current password")
            return
            
        if not new_password:
            messagebox.showerror("خطأ - Error", "يرجى إدخال كلمة المرور الجديدة\nPlease enter new password")
            return
            
        if len(new_password) < 6:
            messagebox.showerror("خطأ - Error", "كلمة المرور يجب أن تكون 6 أحرف على الأقل\nPassword must be at least 6 characters")
            return
            
        if new_password != confirm_password:
            messagebox.showerror("خطأ - Error", "كلمات المرور الجديدة غير متطابقة\nNew passwords do not match")
            return
            
        self.result = {
            'old_password': old_password,
            'new_password': new_password
        }
        
        self.dialog.destroy()
        
    def cancel(self):
        """Cancel dialog"""
        self.result = None
        self.dialog.destroy()
