#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Invoice printing and generation for Qatar POS System
"""

import os
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
import base64
from io import BytesIO

from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.units import mm, inch
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from core.invoice_manager import InvoiceManager
from config.settings import Settings
from utils.logger import get_logger

class InvoicePrinter:
    """Invoice printing and PDF generation"""
    
    def __init__(self):
        self.invoice_manager = InvoiceManager()
        self.settings = Settings()
        self.logger = get_logger()
        
        # Setup fonts for Arabic support
        self.setup_fonts()
        
    def setup_fonts(self):
        """Setup fonts for Arabic text support"""
        try:
            # Try to register Arabic font if available
            font_path = Path("assets/fonts/NotoSansArabic-Regular.ttf")
            if font_path.exists():
                pdfmetrics.registerFont(TTFont('Arabic', str(font_path)))
                self.arabic_font = 'Arabic'
            else:
                # Fallback to default font
                self.arabic_font = 'Helvetica'
                self.logger.warning("Arabic font not found, using Helvetica")
                
        except Exception as e:
            self.logger.error(f"Error setting up fonts: {e}")
            self.arabic_font = 'Helvetica'
            
    def generate_invoice_pdf(self, invoice_id: int, output_path: Optional[str] = None) -> str:
        """Generate PDF invoice"""
        try:
            # Get invoice data
            invoice = self.invoice_manager.get_invoice_by_id(invoice_id)
            if not invoice:
                raise ValueError(f"Invoice not found: {invoice_id}")
                
            # Get shop configuration
            shop_config = self.settings.load_shop_config()
            
            # Determine output path
            if not output_path:
                invoices_dir = Path("invoices")
                invoices_dir.mkdir(exist_ok=True)
                output_path = invoices_dir / f"invoice_{invoice['invoice_number']}.pdf"
            
            # Create PDF document
            doc = SimpleDocTemplate(
                str(output_path),
                pagesize=A4,
                rightMargin=20*mm,
                leftMargin=20*mm,
                topMargin=20*mm,
                bottomMargin=20*mm
            )
            
            # Build content
            story = []
            
            # Add header
            self._add_header(story, shop_config, invoice)
            
            # Add invoice details
            self._add_invoice_details(story, invoice)
            
            # Add items table
            self._add_items_table(story, invoice)
            
            # Add totals
            self._add_totals(story, invoice)
            
            # Add footer
            self._add_footer(story, shop_config, invoice)
            
            # Build PDF
            doc.build(story)
            
            self.logger.info(f"Invoice PDF generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Error generating invoice PDF: {e}")
            raise
            
    def _add_header(self, story, shop_config, invoice):
        """Add invoice header"""
        styles = getSampleStyleSheet()
        
        # Company logo and info
        header_data = []
        
        # Logo column
        logo_cell = ""
        logo_path = Path(shop_config.get('logo_path', 'assets/logo.png'))
        if logo_path.exists():
            try:
                logo_cell = Image(str(logo_path), width=60*mm, height=60*mm)
            except:
                logo_cell = ""
        
        # Company info column
        company_info = [
            Paragraph(shop_config.get('shop_name_ar', ''), 
                     ParagraphStyle('CompanyNameAr', parent=styles['Heading1'], 
                                   fontName=self.arabic_font, fontSize=16, alignment=TA_CENTER)),
            Paragraph(shop_config.get('shop_name_en', ''), 
                     ParagraphStyle('CompanyNameEn', parent=styles['Heading2'], 
                                   fontSize=14, alignment=TA_CENTER)),
            Spacer(1, 5*mm),
            Paragraph(f"العنوان: {shop_config.get('address_ar', '')}", 
                     ParagraphStyle('AddressAr', parent=styles['Normal'], 
                                   fontName=self.arabic_font, fontSize=10, alignment=TA_CENTER)),
            Paragraph(f"Address: {shop_config.get('address_en', '')}", 
                     ParagraphStyle('AddressEn', parent=styles['Normal'], 
                                   fontSize=10, alignment=TA_CENTER)),
            Paragraph(f"Tel: {shop_config.get('phone', '')}", 
                     ParagraphStyle('Phone', parent=styles['Normal'], 
                                   fontSize=10, alignment=TA_CENTER)),
            Paragraph(f"Email: {shop_config.get('email', '')}", 
                     ParagraphStyle('Email', parent=styles['Normal'], 
                                   fontSize=10, alignment=TA_CENTER)),
        ]
        
        # Invoice title column
        invoice_title = [
            Paragraph("فاتورة مبيعات", 
                     ParagraphStyle('InvoiceTitleAr', parent=styles['Heading1'], 
                                   fontName=self.arabic_font, fontSize=18, alignment=TA_CENTER)),
            Paragraph("SALES INVOICE", 
                     ParagraphStyle('InvoiceTitleEn', parent=styles['Heading2'], 
                                   fontSize=16, alignment=TA_CENTER)),
        ]
        
        # Create header table
        header_table = Table([
            [logo_cell, company_info, invoice_title]
        ], colWidths=[60*mm, 80*mm, 60*mm])
        
        header_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        
        story.append(header_table)
        story.append(Spacer(1, 10*mm))
        
    def _add_invoice_details(self, story, invoice):
        """Add invoice details section"""
        styles = getSampleStyleSheet()
        
        # Invoice details
        invoice_date = invoice['created_at'][:10] if invoice['created_at'] else datetime.now().strftime('%Y-%m-%d')
        
        details_data = [
            ["رقم الفاتورة / Invoice No:", invoice['invoice_number'], 
             "التاريخ / Date:", invoice_date],
            ["العميل / Customer:", invoice.get('customer_name', 'نقدي / Cash'), 
             "الكاشير / Cashier:", invoice.get('cashier_name', '')],
        ]
        
        details_table = Table(details_data, colWidths=[40*mm, 60*mm, 30*mm, 40*mm])
        details_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
        ]))
        
        story.append(details_table)
        story.append(Spacer(1, 10*mm))
        
    def _add_items_table(self, story, invoice):
        """Add items table"""
        styles = getSampleStyleSheet()
        
        # Table headers
        headers = [
            "م\nNo.",
            "الصنف\nItem",
            "الكمية\nQty",
            "السعر\nPrice",
            "الخصم\nDiscount",
            "المجموع\nTotal"
        ]
        
        # Table data
        table_data = [headers]
        
        for i, item in enumerate(invoice.get('items', []), 1):
            item_name = f"{item['name_ar']}\n{item['name_en']}"
            row = [
                str(i),
                item_name,
                f"{item['quantity']:.1f}",
                f"{item['unit_price']:.2f}",
                f"{item['discount_amount']:.2f}",
                f"{item['total_amount']:.2f}"
            ]
            table_data.append(row)
        
        # Create table
        items_table = Table(table_data, colWidths=[15*mm, 60*mm, 20*mm, 25*mm, 25*mm, 25*mm])
        
        # Table style
        table_style = [
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]
        
        items_table.setStyle(TableStyle(table_style))
        
        story.append(items_table)
        story.append(Spacer(1, 10*mm))
        
    def _add_totals(self, story, invoice):
        """Add totals section"""
        styles = getSampleStyleSheet()
        
        # Calculate totals
        subtotal = invoice.get('subtotal', 0)
        discount = invoice.get('discount_amount', 0)
        tax = invoice.get('tax_amount', 0)
        total = invoice.get('total_amount', 0)
        
        # Totals data
        totals_data = [
            ["المجموع الفرعي / Subtotal:", f"{subtotal:.2f} QAR"],
            ["الخصم / Discount:", f"{discount:.2f} QAR"],
            ["الضريبة / Tax:", f"{tax:.2f} QAR"],
            ["المجموع الكلي / Total:", f"{total:.2f} QAR"],
        ]
        
        totals_table = Table(totals_data, colWidths=[100*mm, 40*mm])
        totals_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
        ]))
        
        story.append(totals_table)
        story.append(Spacer(1, 10*mm))
        
    def _add_footer(self, story, shop_config, invoice):
        """Add invoice footer with QR code"""
        styles = getSampleStyleSheet()
        
        # Generate QR code
        qr_code_data = self.invoice_manager.generate_qr_code(invoice)
        
        # Footer content
        footer_data = []
        
        # QR Code column
        qr_cell = ""
        if qr_code_data:
            try:
                qr_image_data = base64.b64decode(qr_code_data)
                qr_image = Image(BytesIO(qr_image_data), width=40*mm, height=40*mm)
                qr_cell = qr_image
            except:
                qr_cell = "QR Code"
        
        # Footer text
        footer_text = [
            Paragraph("شكراً لتعاملكم معنا", 
                     ParagraphStyle('ThankYouAr', parent=styles['Normal'], 
                                   fontName=self.arabic_font, fontSize=12, alignment=TA_CENTER)),
            Paragraph("Thank you for your business", 
                     ParagraphStyle('ThankYouEn', parent=styles['Normal'], 
                                   fontSize=12, alignment=TA_CENTER)),
            Spacer(1, 5*mm),
            Paragraph(f"السجل التجاري: {shop_config.get('commercial_registration', '')}", 
                     ParagraphStyle('CRAr', parent=styles['Normal'], 
                                   fontName=self.arabic_font, fontSize=8, alignment=TA_CENTER)),
            Paragraph(f"الرقم الضريبي: {shop_config.get('tax_number', '')}", 
                     ParagraphStyle('TaxAr', parent=styles['Normal'], 
                                   fontName=self.arabic_font, fontSize=8, alignment=TA_CENTER)),
        ]
        
        # Create footer table
        footer_table = Table([
            [qr_cell, footer_text]
        ], colWidths=[50*mm, 120*mm])
        
        footer_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        story.append(footer_table)
        
    def print_thermal_receipt(self, invoice_id: int) -> bool:
        """Print thermal receipt (simplified format)"""
        try:
            # Get invoice data
            invoice = self.invoice_manager.get_invoice_by_id(invoice_id)
            if not invoice:
                raise ValueError(f"Invoice not found: {invoice_id}")
                
            # Get shop configuration
            shop_config = self.settings.load_shop_config()
            
            # Generate receipt text
            receipt_text = self._generate_receipt_text(invoice, shop_config)
            
            # For now, save to file (in real implementation, send to thermal printer)
            receipts_dir = Path("receipts")
            receipts_dir.mkdir(exist_ok=True)
            receipt_file = receipts_dir / f"receipt_{invoice['invoice_number']}.txt"
            
            with open(receipt_file, 'w', encoding='utf-8') as f:
                f.write(receipt_text)
                
            self.logger.info(f"Thermal receipt generated: {receipt_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error printing thermal receipt: {e}")
            return False
            
    def _generate_receipt_text(self, invoice, shop_config) -> str:
        """Generate thermal receipt text"""
        lines = []
        
        # Header
        lines.append("=" * 40)
        lines.append(shop_config.get('shop_name_ar', '').center(40))
        lines.append(shop_config.get('shop_name_en', '').center(40))
        lines.append(shop_config.get('phone', '').center(40))
        lines.append("=" * 40)
        lines.append("")
        
        # Invoice details
        lines.append(f"Invoice: {invoice['invoice_number']}")
        lines.append(f"Date: {invoice['created_at'][:16] if invoice['created_at'] else ''}")
        lines.append(f"Cashier: {invoice.get('cashier_name', '')}")
        if invoice.get('customer_name'):
            lines.append(f"Customer: {invoice['customer_name']}")
        lines.append("-" * 40)
        
        # Items
        for item in invoice.get('items', []):
            lines.append(f"{item['name_en'][:30]}")
            lines.append(f"  {item['quantity']:.1f} x {item['unit_price']:.2f} = {item['total_amount']:.2f}")
        
        lines.append("-" * 40)
        
        # Totals
        lines.append(f"Subtotal: {invoice.get('subtotal', 0):.2f} QAR")
        if invoice.get('discount_amount', 0) > 0:
            lines.append(f"Discount: {invoice.get('discount_amount', 0):.2f} QAR")
        if invoice.get('tax_amount', 0) > 0:
            lines.append(f"Tax: {invoice.get('tax_amount', 0):.2f} QAR")
        lines.append(f"TOTAL: {invoice.get('total_amount', 0):.2f} QAR")
        lines.append("")
        
        # Footer
        lines.append("Thank you for your business!")
        lines.append("شكراً لتعاملكم معنا")
        lines.append("=" * 40)
        
        return "\n".join(lines)
