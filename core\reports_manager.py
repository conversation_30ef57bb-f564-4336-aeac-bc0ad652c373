#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reports and analytics manager for Qatar POS System
"""

import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import json

from database.database_manager import DatabaseManager
from core.auth import auth_manager
from config.settings import Settings
from utils.logger import get_logger

class ReportsManager:
    """Reports and analytics manager"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.settings = Settings()
        self.logger = get_logger()
        
    def get_sales_report(self, start_date: str, end_date: str, 
                        group_by: str = 'day') -> Dict[str, Any]:
        """Generate sales report"""
        try:
            if not auth_manager.can_access_feature('reports'):
                raise PermissionError("Not authorized for reports")
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Base query for sales data
                base_sql = """
                    SELECT 
                        DATE(i.created_at) as sale_date,
                        COUNT(i.id) as total_invoices,
                        SUM(i.total_amount) as total_sales,
                        SUM(i.subtotal) as total_subtotal,
                        SUM(i.tax_amount) as total_tax,
                        SUM(i.discount_amount) as total_discount,
                        AVG(i.total_amount) as avg_invoice_value
                    FROM invoices i
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                """
                
                if group_by == 'day':
                    sql = base_sql + " GROUP BY DATE(i.created_at) ORDER BY sale_date"
                elif group_by == 'month':
                    sql = base_sql.replace('DATE(i.created_at)', 'strftime("%Y-%m", i.created_at)')
                    sql = sql.replace('sale_date', 'sale_month')
                    sql += " GROUP BY strftime('%Y-%m', i.created_at) ORDER BY sale_month"
                elif group_by == 'year':
                    sql = base_sql.replace('DATE(i.created_at)', 'strftime("%Y", i.created_at)')
                    sql = sql.replace('sale_date', 'sale_year')
                    sql += " GROUP BY strftime('%Y', i.created_at) ORDER BY sale_year"
                else:
                    sql = base_sql + " GROUP BY DATE(i.created_at) ORDER BY sale_date"
                
                cursor.execute(sql, (start_date, end_date))
                sales_data = [dict(row) for row in cursor.fetchall()]
                
                # Get summary statistics
                cursor.execute("""
                    SELECT 
                        COUNT(i.id) as total_invoices,
                        SUM(i.total_amount) as total_sales,
                        SUM(i.subtotal) as total_subtotal,
                        SUM(i.tax_amount) as total_tax,
                        SUM(i.discount_amount) as total_discount,
                        AVG(i.total_amount) as avg_invoice_value,
                        MIN(i.total_amount) as min_invoice_value,
                        MAX(i.total_amount) as max_invoice_value
                    FROM invoices i
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                """, (start_date, end_date))
                
                summary = dict(cursor.fetchone())
                
                # Get payment method breakdown
                cursor.execute("""
                    SELECT 
                        i.payment_method,
                        COUNT(i.id) as count,
                        SUM(i.total_amount) as total
                    FROM invoices i
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                    GROUP BY i.payment_method
                    ORDER BY total DESC
                """, (start_date, end_date))
                
                payment_methods = [dict(row) for row in cursor.fetchall()]
                
                return {
                    'period': {'start': start_date, 'end': end_date, 'group_by': group_by},
                    'sales_data': sales_data,
                    'summary': summary,
                    'payment_methods': payment_methods,
                    'generated_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error generating sales report: {e}")
            raise
            
    def get_product_sales_report(self, start_date: str, end_date: str, 
                               limit: int = 50) -> Dict[str, Any]:
        """Generate product sales report"""
        try:
            if not auth_manager.can_access_feature('reports'):
                raise PermissionError("Not authorized for reports")
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Top selling products by quantity
                cursor.execute("""
                    SELECT 
                        p.name_ar,
                        p.name_en,
                        p.barcode,
                        SUM(ii.quantity) as total_quantity,
                        SUM(ii.total_amount) as total_sales,
                        AVG(ii.unit_price) as avg_price,
                        COUNT(DISTINCT i.id) as invoice_count
                    FROM invoice_items ii
                    JOIN invoices i ON ii.invoice_id = i.id
                    JOIN products p ON ii.product_id = p.id
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                    GROUP BY p.id
                    ORDER BY total_quantity DESC
                    LIMIT ?
                """, (start_date, end_date, limit))
                
                top_products_by_qty = [dict(row) for row in cursor.fetchall()]
                
                # Top selling products by revenue
                cursor.execute("""
                    SELECT 
                        p.name_ar,
                        p.name_en,
                        p.barcode,
                        SUM(ii.quantity) as total_quantity,
                        SUM(ii.total_amount) as total_sales,
                        AVG(ii.unit_price) as avg_price,
                        COUNT(DISTINCT i.id) as invoice_count
                    FROM invoice_items ii
                    JOIN invoices i ON ii.invoice_id = i.id
                    JOIN products p ON ii.product_id = p.id
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                    GROUP BY p.id
                    ORDER BY total_sales DESC
                    LIMIT ?
                """, (start_date, end_date, limit))
                
                top_products_by_sales = [dict(row) for row in cursor.fetchall()]
                
                # Category performance
                cursor.execute("""
                    SELECT 
                        c.name_ar as category_name_ar,
                        c.name_en as category_name_en,
                        SUM(ii.quantity) as total_quantity,
                        SUM(ii.total_amount) as total_sales,
                        COUNT(DISTINCT p.id) as product_count,
                        COUNT(DISTINCT i.id) as invoice_count
                    FROM invoice_items ii
                    JOIN invoices i ON ii.invoice_id = i.id
                    JOIN products p ON ii.product_id = p.id
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                    GROUP BY c.id
                    ORDER BY total_sales DESC
                """, (start_date, end_date))
                
                category_performance = [dict(row) for row in cursor.fetchall()]
                
                return {
                    'period': {'start': start_date, 'end': end_date},
                    'top_products_by_quantity': top_products_by_qty,
                    'top_products_by_sales': top_products_by_sales,
                    'category_performance': category_performance,
                    'generated_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error generating product sales report: {e}")
            raise
            
    def get_profit_report(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Generate profit and loss report"""
        try:
            if not auth_manager.can_access_feature('financial_reports'):
                raise PermissionError("Not authorized for financial reports")
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Calculate profit by product
                cursor.execute("""
                    SELECT 
                        p.name_ar,
                        p.name_en,
                        p.cost_price,
                        SUM(ii.quantity) as total_quantity,
                        SUM(ii.total_amount) as total_revenue,
                        SUM(ii.quantity * p.cost_price) as total_cost,
                        SUM(ii.total_amount - (ii.quantity * p.cost_price)) as total_profit,
                        (SUM(ii.total_amount - (ii.quantity * p.cost_price)) / SUM(ii.total_amount)) * 100 as profit_margin
                    FROM invoice_items ii
                    JOIN invoices i ON ii.invoice_id = i.id
                    JOIN products p ON ii.product_id = p.id
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                    GROUP BY p.id
                    ORDER BY total_profit DESC
                """, (start_date, end_date))
                
                product_profits = [dict(row) for row in cursor.fetchall()]
                
                # Overall profit summary
                cursor.execute("""
                    SELECT 
                        SUM(ii.total_amount) as total_revenue,
                        SUM(ii.quantity * p.cost_price) as total_cost,
                        SUM(ii.total_amount - (ii.quantity * p.cost_price)) as total_profit,
                        COUNT(DISTINCT i.id) as total_invoices,
                        COUNT(DISTINCT ii.product_id) as products_sold
                    FROM invoice_items ii
                    JOIN invoices i ON ii.invoice_id = i.id
                    JOIN products p ON ii.product_id = p.id
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                """, (start_date, end_date))
                
                summary = dict(cursor.fetchone())
                
                # Calculate profit margin
                if summary['total_revenue'] and summary['total_revenue'] > 0:
                    summary['profit_margin'] = (summary['total_profit'] / summary['total_revenue']) * 100
                else:
                    summary['profit_margin'] = 0
                
                # Daily profit trend
                cursor.execute("""
                    SELECT 
                        DATE(i.created_at) as date,
                        SUM(ii.total_amount) as daily_revenue,
                        SUM(ii.quantity * p.cost_price) as daily_cost,
                        SUM(ii.total_amount - (ii.quantity * p.cost_price)) as daily_profit
                    FROM invoice_items ii
                    JOIN invoices i ON ii.invoice_id = i.id
                    JOIN products p ON ii.product_id = p.id
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                    GROUP BY DATE(i.created_at)
                    ORDER BY date
                """, (start_date, end_date))
                
                daily_profits = [dict(row) for row in cursor.fetchall()]
                
                return {
                    'period': {'start': start_date, 'end': end_date},
                    'summary': summary,
                    'product_profits': product_profits,
                    'daily_profits': daily_profits,
                    'generated_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error generating profit report: {e}")
            raise
            
    def get_customer_report(self, start_date: str, end_date: str, 
                          limit: int = 50) -> Dict[str, Any]:
        """Generate customer analysis report"""
        try:
            if not auth_manager.can_access_feature('reports'):
                raise PermissionError("Not authorized for reports")
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Top customers by sales
                cursor.execute("""
                    SELECT 
                        c.name,
                        c.phone,
                        c.email,
                        COUNT(i.id) as total_orders,
                        SUM(i.total_amount) as total_spent,
                        AVG(i.total_amount) as avg_order_value,
                        MAX(i.created_at) as last_purchase,
                        c.current_balance
                    FROM customers c
                    JOIN invoices i ON c.id = i.customer_id
                    WHERE i.status = 'completed'
                    AND DATE(i.created_at) BETWEEN ? AND ?
                    GROUP BY c.id
                    ORDER BY total_spent DESC
                    LIMIT ?
                """, (start_date, end_date, limit))
                
                top_customers = [dict(row) for row in cursor.fetchall()]
                
                # Customer statistics
                cursor.execute("""
                    SELECT 
                        COUNT(DISTINCT c.id) as total_customers,
                        COUNT(DISTINCT i.customer_id) as active_customers,
                        AVG(customer_stats.total_spent) as avg_customer_value,
                        AVG(customer_stats.total_orders) as avg_orders_per_customer
                    FROM customers c
                    LEFT JOIN (
                        SELECT 
                            customer_id,
                            SUM(total_amount) as total_spent,
                            COUNT(id) as total_orders
                        FROM invoices
                        WHERE status = 'completed'
                        AND DATE(created_at) BETWEEN ? AND ?
                        GROUP BY customer_id
                    ) customer_stats ON c.id = customer_stats.customer_id
                """, (start_date, end_date))
                
                customer_stats = dict(cursor.fetchone())
                
                # Customers with outstanding credit
                cursor.execute("""
                    SELECT 
                        c.name,
                        c.phone,
                        c.current_balance,
                        c.credit_limit,
                        (c.current_balance / c.credit_limit) * 100 as credit_utilization
                    FROM customers c
                    WHERE c.current_balance > 0
                    ORDER BY c.current_balance DESC
                """)
                
                credit_customers = [dict(row) for row in cursor.fetchall()]
                
                return {
                    'period': {'start': start_date, 'end': end_date},
                    'top_customers': top_customers,
                    'customer_statistics': customer_stats,
                    'credit_customers': credit_customers,
                    'generated_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error generating customer report: {e}")
            raise
            
    def get_inventory_report(self) -> Dict[str, Any]:
        """Generate inventory status report"""
        try:
            if not auth_manager.can_access_feature('inventory'):
                raise PermissionError("Not authorized for inventory reports")
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Low stock products
                cursor.execute("""
                    SELECT 
                        p.name_ar,
                        p.name_en,
                        p.barcode,
                        p.stock_quantity,
                        p.min_stock_level,
                        p.cost_price,
                        p.selling_price,
                        c.name_ar as category_name_ar,
                        c.name_en as category_name_en
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1
                    AND p.stock_quantity <= p.min_stock_level
                    ORDER BY (p.stock_quantity - p.min_stock_level) ASC
                """)
                
                low_stock_products = [dict(row) for row in cursor.fetchall()]
                
                # Out of stock products
                cursor.execute("""
                    SELECT 
                        p.name_ar,
                        p.name_en,
                        p.barcode,
                        p.cost_price,
                        p.selling_price,
                        c.name_ar as category_name_ar,
                        c.name_en as category_name_en
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1
                    AND p.stock_quantity = 0
                    ORDER BY p.name_en
                """)
                
                out_of_stock_products = [dict(row) for row in cursor.fetchall()]
                
                # Inventory value by category
                cursor.execute("""
                    SELECT 
                        c.name_ar as category_name_ar,
                        c.name_en as category_name_en,
                        COUNT(p.id) as product_count,
                        SUM(p.stock_quantity) as total_quantity,
                        SUM(p.stock_quantity * p.cost_price) as total_cost_value,
                        SUM(p.stock_quantity * p.selling_price) as total_selling_value
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1
                    GROUP BY c.id
                    ORDER BY total_cost_value DESC
                """)
                
                inventory_by_category = [dict(row) for row in cursor.fetchall()]
                
                # Overall inventory statistics
                cursor.execute("""
                    SELECT 
                        COUNT(p.id) as total_products,
                        COUNT(CASE WHEN p.stock_quantity > 0 THEN 1 END) as in_stock_products,
                        COUNT(CASE WHEN p.stock_quantity = 0 THEN 1 END) as out_of_stock_products,
                        COUNT(CASE WHEN p.stock_quantity <= p.min_stock_level THEN 1 END) as low_stock_products,
                        SUM(p.stock_quantity) as total_quantity,
                        SUM(p.stock_quantity * p.cost_price) as total_cost_value,
                        SUM(p.stock_quantity * p.selling_price) as total_selling_value
                    FROM products p
                    WHERE p.is_active = 1
                """)
                
                inventory_stats = dict(cursor.fetchone())
                
                return {
                    'low_stock_products': low_stock_products,
                    'out_of_stock_products': out_of_stock_products,
                    'inventory_by_category': inventory_by_category,
                    'inventory_statistics': inventory_stats,
                    'generated_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error generating inventory report: {e}")
            raise
