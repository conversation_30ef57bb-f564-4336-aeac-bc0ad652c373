#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration management for Qatar POS System
"""

import os
import json
import configparser
from pathlib import Path
from typing import Any, Dict, Optional

class Settings:
    """Application settings manager"""
    
    def __init__(self):
        self.config_dir = Path(__file__).parent
        self.config_file = self.config_dir / "app_config.ini"
        self.shop_config_file = self.config_dir / "shop_config.json"
        
        # Default configuration
        self.defaults = {
            'language': 'ar',  # Arabic by default
            'currency': 'QAR',
            'date_format': 'hijri',
            'theme': 'light',
            'database_path': 'database/pos_system.db',
            'backup_interval': '24',  # hours
            'auto_backup': 'true',
            'thermal_printer': 'false',
            'barcode_scanner': 'false',
            'cash_drawer': 'false',
            'vat_rate': '0.0',  # Qatar VAT rate
            'invoice_prefix': 'INV',
            'receipt_prefix': 'RCP',
        }
        
        self.config = configparser.ConfigParser()
        self.load_config()
        
    def load_config(self):
        """Load configuration from file or create with defaults"""
        if self.config_file.exists():
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self.create_default_config()
            
    def create_default_config(self):
        """Create default configuration file"""
        self.config['DEFAULT'] = self.defaults
        
        # Application settings
        self.config['APP'] = {
            'language': 'ar',
            'theme': 'light',
            'auto_backup': 'true',
            'backup_interval': '24'
        }
        
        # Database settings
        self.config['DATABASE'] = {
            'path': 'database/pos_system.db',
            'backup_path': 'database/backups/'
        }
        
        # Hardware settings
        self.config['HARDWARE'] = {
            'thermal_printer': 'false',
            'printer_port': 'COM1',
            'barcode_scanner': 'false',
            'scanner_port': 'COM2',
            'cash_drawer': 'false'
        }
        
        # Business settings
        self.config['BUSINESS'] = {
            'currency': 'QAR',
            'date_format': 'hijri',
            'vat_rate': '0.0',
            'invoice_prefix': 'INV',
            'receipt_prefix': 'RCP'
        }
        
        self.save_config()
        
    def save_config(self):
        """Save configuration to file"""
        os.makedirs(self.config_dir, exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
            
    def get(self, key: str, section: str = 'DEFAULT', default: Any = None) -> str:
        """Get configuration value"""
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default or self.defaults.get(key, '')
            
    def set(self, key: str, value: str, section: str = 'DEFAULT'):
        """Set configuration value"""
        if section not in self.config:
            self.config.add_section(section)
        self.config.set(section, key, str(value))
        self.save_config()
        
    def get_bool(self, key: str, section: str = 'DEFAULT', default: bool = False) -> bool:
        """Get boolean configuration value"""
        value = self.get(key, section, str(default))
        return value.lower() in ('true', '1', 'yes', 'on')
        
    def get_int(self, key: str, section: str = 'DEFAULT', default: int = 0) -> int:
        """Get integer configuration value"""
        try:
            return int(self.get(key, section, str(default)))
        except ValueError:
            return default
            
    def get_float(self, key: str, section: str = 'DEFAULT', default: float = 0.0) -> float:
        """Get float configuration value"""
        try:
            return float(self.get(key, section, str(default)))
        except ValueError:
            return default
            
    def load_shop_config(self) -> Dict[str, Any]:
        """Load shop-specific configuration"""
        if self.shop_config_file.exists():
            with open(self.shop_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return self.create_default_shop_config()
            
    def create_default_shop_config(self) -> Dict[str, Any]:
        """Create default shop configuration"""
        shop_config = {
            'shop_name_ar': 'متجر قطر',
            'shop_name_en': 'Qatar Store',
            'address_ar': 'الدوحة، قطر',
            'address_en': 'Doha, Qatar',
            'phone': '+974 XXXX XXXX',
            'email': '<EMAIL>',
            'commercial_registration': 'CR-XXXXXXX',
            'tax_number': 'TAX-XXXXXXX',
            'logo_path': 'assets/logo.png',
            'stamp_path': 'assets/stamp.png',
            'currency_symbol': 'ر.ق',
            'currency_code': 'QAR'
        }
        
        self.save_shop_config(shop_config)
        return shop_config
        
    def save_shop_config(self, config: Dict[str, Any]):
        """Save shop configuration"""
        os.makedirs(self.config_dir, exist_ok=True)
        with open(self.shop_config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
