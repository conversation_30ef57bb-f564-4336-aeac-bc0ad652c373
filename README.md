# Qatar POS System - نظام نقاط البيع القطري

A comprehensive Point of Sale (POS) system designed specifically for Qatar's retail market, complying with local laws and requirements.

## Features - المميزات

### 🌍 Qatar-Specific Features
- **Bilingual Support**: Arabic and English interface
- **Currency**: Qatari Riyal (QAR) with multi-currency support
- **Date Formats**: Hijri and Gregorian calendar support
- **Tax Compliance**: Qatar Tax Authority compliant invoicing
- **Official Invoice Format**: QR codes, official numbering, and stamps

### 🏪 Business Features
- **Multi-User System**: Manager, Seller, Accountant, Inventory roles
- **Product Management**: Inventory tracking, categories, alerts
- **Customer Management**: Registration, purchase history, WhatsApp notifications
- **Invoice Management**: Sales, returns, automatic numbering
- **Reports & Analytics**: Daily/monthly reports, profit/loss analysis
- **Hardware Integration**: Barcode scanners, thermal printers, cash drawers

## Installation - التثبيت

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd qatar-pos-system
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```bash
   python main.py
   ```

## Project Structure - هيكل المشروع

```
qatar-pos-system/
├── main.py                 # Application entry point
├── config/                 # Configuration files
├── database/              # Database models and migrations
├── ui/                    # User interface components
├── core/                  # Core business logic
├── utils/                 # Utility functions
├── localization/          # Language files
├── assets/               # Images, logos, templates
├── reports/              # Report templates
├── tests/                # Unit tests
└── docs/                 # Documentation
```

## Usage - الاستخدام

### Default Login Credentials
- **Username**: admin
- **Password**: admin123

### Supported Hardware
- Thermal printers (ESC/POS compatible)
- Barcode scanners (USB/Serial)
- Cash drawers
- Touch screen monitors

## License - الترخيص

This project is licensed under the MIT License.

## Support - الدعم

For support and questions, please contact the development team.
