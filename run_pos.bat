@echo off
echo.
echo ========================================
echo   Qatar POS System - نظام نقاط البيع القطري
echo ========================================
echo.
echo Starting Qatar POS System...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Check if required packages are installed
echo Checking dependencies...
python -c "import tkinter, sqlite3" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Run the application
echo.
echo Launching Qatar POS System...
echo.
python main.py

if errorlevel 1 (
    echo.
    echo ERROR: Application failed to start
    pause
)

echo.
echo Application closed.
pause
