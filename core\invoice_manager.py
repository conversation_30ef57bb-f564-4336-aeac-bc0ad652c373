#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Invoice management for Qatar POS System
"""

import sqlite3
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
import qrcode
from io import BytesIO
import base64

from database.models import Invoice, InvoiceItem, PaymentMethod, InvoiceStatus
from database.database_manager import DatabaseManager
from core.auth import auth_manager
from core.product_manager import ProductManager
from core.customer_manager import CustomerManager
from config.settings import Settings
from utils.logger import get_logger

class InvoiceManager:
    """Invoice management class"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.product_manager = ProductManager()
        self.customer_manager = CustomerManager()
        self.settings = Settings()
        self.logger = get_logger()
        
    def create_invoice(self, customer_id: Optional[int] = None, 
                      items: List[Dict[str, Any]] = None) -> Optional[int]:
        """Create a new invoice"""
        try:
            if not auth_manager.can_access_feature('sales'):
                self.logger.warning("Unauthorized access to invoice creation")
                return None
                
            if not items:
                items = []
                
            user_id = auth_manager.get_current_user().user_id
            invoice_number = self._generate_invoice_number()
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Create invoice
                cursor.execute("""
                    INSERT INTO invoices (
                        invoice_number, customer_id, user_id, status
                    ) VALUES (?, ?, ?, ?)
                """, (invoice_number, customer_id, user_id, InvoiceStatus.PENDING.value))
                
                invoice_id = cursor.lastrowid
                
                # Add items if provided
                if items:
                    for item in items:
                        self._add_invoice_item(cursor, invoice_id, item)
                        
                # Calculate totals
                self._calculate_invoice_totals(cursor, invoice_id)
                
                conn.commit()
                
                self.logger.info(f"Invoice created: {invoice_number} (ID: {invoice_id})")
                return invoice_id
                
        except Exception as e:
            self.logger.error(f"Error creating invoice: {e}")
            return None
            
    def add_item_to_invoice(self, invoice_id: int, product_id: int, 
                           quantity: float, unit_price: Optional[float] = None,
                           discount_amount: float = 0.0) -> bool:
        """Add item to existing invoice"""
        try:
            if not auth_manager.can_access_feature('sales'):
                return False
                
            # Get product info
            product = self.product_manager.get_product_by_id(product_id)
            if not product:
                self.logger.error(f"Product not found: ID {product_id}")
                return False
                
            # Check stock availability
            if product['stock_quantity'] < quantity:
                self.logger.error(f"Insufficient stock for product ID {product_id}")
                return False
                
            # Use product selling price if not provided
            if unit_price is None:
                unit_price = product['selling_price']
                
            item_data = {
                'product_id': product_id,
                'quantity': quantity,
                'unit_price': unit_price,
                'discount_amount': discount_amount
            }
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Check if invoice exists and is pending
                cursor.execute("""
                    SELECT status FROM invoices WHERE id = ?
                """, (invoice_id,))
                
                invoice = cursor.fetchone()
                if not invoice or invoice['status'] != InvoiceStatus.PENDING.value:
                    self.logger.error(f"Invoice not found or not editable: ID {invoice_id}")
                    return False
                    
                self._add_invoice_item(cursor, invoice_id, item_data)
                self._calculate_invoice_totals(cursor, invoice_id)
                
                conn.commit()
                
                self.logger.info(f"Item added to invoice {invoice_id}: Product {product_id}, Qty {quantity}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error adding item to invoice: {e}")
            return False
            
    def remove_item_from_invoice(self, invoice_id: int, item_id: int) -> bool:
        """Remove item from invoice"""
        try:
            if not auth_manager.can_access_feature('sales'):
                return False
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Check if invoice is editable
                cursor.execute("""
                    SELECT status FROM invoices WHERE id = ?
                """, (invoice_id,))
                
                invoice = cursor.fetchone()
                if not invoice or invoice['status'] != InvoiceStatus.PENDING.value:
                    return False
                    
                # Remove item
                cursor.execute("""
                    DELETE FROM invoice_items WHERE id = ? AND invoice_id = ?
                """, (item_id, invoice_id))
                
                if cursor.rowcount > 0:
                    self._calculate_invoice_totals(cursor, invoice_id)
                    conn.commit()
                    
                    self.logger.info(f"Item removed from invoice {invoice_id}: Item {item_id}")
                    return True
                    
                return False
                
        except Exception as e:
            self.logger.error(f"Error removing item from invoice: {e}")
            return False
            
    def complete_invoice(self, invoice_id: int, payment_method: PaymentMethod,
                        discount_amount: float = 0.0, notes: str = "") -> bool:
        """Complete an invoice (finalize the sale)"""
        try:
            if not auth_manager.can_access_feature('sales'):
                return False
                
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get invoice details
                cursor.execute("""
                    SELECT * FROM invoices WHERE id = ? AND status = ?
                """, (invoice_id, InvoiceStatus.PENDING.value))
                
                invoice = cursor.fetchone()
                if not invoice:
                    self.logger.error(f"Invoice not found or not pending: ID {invoice_id}")
                    return False
                    
                # Get invoice items
                cursor.execute("""
                    SELECT ii.*, p.name_en, p.stock_quantity
                    FROM invoice_items ii
                    JOIN products p ON ii.product_id = p.id
                    WHERE ii.invoice_id = ?
                """, (invoice_id,))
                
                items = cursor.fetchall()
                if not items:
                    self.logger.error(f"No items in invoice: ID {invoice_id}")
                    return False
                    
                # Check stock availability for all items
                for item in items:
                    if item['stock_quantity'] < item['quantity']:
                        self.logger.error(f"Insufficient stock for {item['name_en']}")
                        return False
                        
                # Update stock quantities
                for item in items:
                    self.product_manager.update_stock(
                        item['product_id'], -item['quantity'], 'out',
                        'sale', invoice_id, f"Sale - Invoice {invoice['invoice_number']}"
                    )
                    
                # Update invoice
                vat_rate = self.settings.get_float('vat_rate', 'BUSINESS', 0.0)
                subtotal = invoice['subtotal']
                total_discount = discount_amount
                tax_amount = (subtotal - total_discount) * (vat_rate / 100)
                total_amount = subtotal - total_discount + tax_amount
                
                cursor.execute("""
                    UPDATE invoices SET
                        discount_amount = ?,
                        tax_amount = ?,
                        total_amount = ?,
                        payment_method = ?,
                        status = ?,
                        notes = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (total_discount, tax_amount, total_amount, 
                     payment_method.value, InvoiceStatus.COMPLETED.value, 
                     notes, invoice_id))
                
                # Update customer balance if credit payment
                if payment_method == PaymentMethod.CREDIT and invoice['customer_id']:
                    self.customer_manager.update_customer_balance(
                        invoice['customer_id'], total_amount, 'add'
                    )
                    
                conn.commit()
                
                self.logger.info(f"Invoice completed: {invoice['invoice_number']}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error completing invoice: {e}")
            return False
            
    def get_invoice_by_id(self, invoice_id: int) -> Optional[Dict[str, Any]]:
        """Get invoice with items"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get invoice
                cursor.execute("""
                    SELECT i.*, c.name as customer_name, u.full_name as cashier_name
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    LEFT JOIN users u ON i.user_id = u.id
                    WHERE i.id = ?
                """, (invoice_id,))
                
                invoice = cursor.fetchone()
                if not invoice:
                    return None
                    
                invoice_dict = dict(invoice)
                
                # Get items
                cursor.execute("""
                    SELECT ii.*, p.name_ar, p.name_en, p.barcode, p.unit
                    FROM invoice_items ii
                    JOIN products p ON ii.product_id = p.id
                    WHERE ii.invoice_id = ?
                    ORDER BY ii.id
                """, (invoice_id,))
                
                invoice_dict['items'] = [dict(row) for row in cursor.fetchall()]
                
                return invoice_dict

        except Exception as e:
            self.logger.error(f"Error fetching invoice: {e}")
            return None

    def _generate_invoice_number(self) -> str:
        """Generate unique invoice number"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Get next invoice number from settings
                cursor.execute("""
                    SELECT value FROM system_settings WHERE key = 'next_invoice_number'
                """)

                row = cursor.fetchone()
                next_number = int(row['value']) if row else 1

                # Get prefix from settings
                prefix = self.settings.get('invoice_prefix', 'BUSINESS', 'INV')

                # Generate invoice number and ensure uniqueness
                while True:
                    invoice_number = f"{prefix}-{next_number:06d}"

                    # Check if this number already exists
                    cursor.execute("""
                        SELECT COUNT(*) FROM invoices WHERE invoice_number = ?
                    """, (invoice_number,))

                    if cursor.fetchone()[0] == 0:
                        # Number is unique, update settings and return
                        cursor.execute("""
                            UPDATE system_settings SET value = ? WHERE key = 'next_invoice_number'
                        """, (str(next_number + 1),))
                        conn.commit()
                        return invoice_number
                    else:
                        # Number exists, try next one
                        next_number += 1

        except Exception as e:
            self.logger.error(f"Error generating invoice number: {e}")
            # Fallback to timestamp-based number
            return f"INV-{int(datetime.now().timestamp())}"

    def _add_invoice_item(self, cursor: sqlite3.Cursor, invoice_id: int, item_data: Dict[str, Any]):
        """Add item to invoice"""
        total_amount = (item_data['quantity'] * item_data['unit_price']) - item_data.get('discount_amount', 0)

        cursor.execute("""
            INSERT INTO invoice_items (
                invoice_id, product_id, quantity, unit_price, discount_amount, total_amount
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, (
            invoice_id, item_data['product_id'], item_data['quantity'],
            item_data['unit_price'], item_data.get('discount_amount', 0), total_amount
        ))

    def _calculate_invoice_totals(self, cursor: sqlite3.Cursor, invoice_id: int):
        """Calculate and update invoice totals"""
        cursor.execute("""
            SELECT COALESCE(SUM(total_amount), 0) as subtotal
            FROM invoice_items WHERE invoice_id = ?
        """, (invoice_id,))

        subtotal = cursor.fetchone()['subtotal']

        cursor.execute("""
            UPDATE invoices SET subtotal = ? WHERE id = ?
        """, (subtotal, invoice_id))

    def generate_qr_code(self, invoice_data: Dict[str, Any]) -> str:
        """Generate QR code for invoice (Qatar e-invoicing compliance)"""
        try:
            # Qatar e-invoicing QR code format
            shop_config = self.settings.load_shop_config()

            qr_data = (
                f"Company: {shop_config.get('shop_name_en', '')}\n"
                f"Tax Number: {shop_config.get('tax_number', '')}\n"
                f"Invoice: {invoice_data.get('invoice_number', '')}\n"
                f"Date: {invoice_data.get('created_at', '')}\n"
                f"Total: {invoice_data.get('total_amount', 0):.2f} QAR"
            )

            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(qr_data)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64 for embedding in templates
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            return img_str

        except Exception as e:
            self.logger.error(f"Error generating QR code: {e}")
            return ""
