#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
System settings window for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
from PIL import Image, ImageTk
import os
from pathlib import Path
import shutil

from core.auth import auth_manager
from config.settings import Settings
from utils.logger import get_logger

class SettingsWindow:
    """System settings window"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.settings = Settings()
        self.logger = get_logger()
        
        self.window = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the settings interface"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("إعدادات النظام - System Settings")
        self.window.geometry("800x600")
        
        # Create main layout
        self.create_notebook()
        self.create_buttons()
        
        # Load current settings
        self.load_settings()
        
    def create_notebook(self):
        """Create settings notebook with tabs"""
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # General settings tab
        self.create_general_tab()
        
        # Shop information tab
        self.create_shop_tab()
        
        # Business settings tab
        self.create_business_tab()
        
        # Hardware settings tab
        self.create_hardware_tab()
        
        # Appearance settings tab
        self.create_appearance_tab()
        
        # Backup settings tab
        self.create_backup_tab()
        
    def create_general_tab(self):
        """Create general settings tab"""
        general_frame = ttk.Frame(self.notebook)
        self.notebook.add(general_frame, text="عام - General")
        
        # Language settings
        lang_frame = ttk.LabelFrame(general_frame, text="اللغة - Language", padding="10")
        lang_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(lang_frame, text="لغة النظام - System Language:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.language_var = tk.StringVar()
        language_combo = ttk.Combobox(lang_frame, textvariable=self.language_var, 
                                    values=[("ar", "العربية"), ("en", "English")], state="readonly")
        language_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Date format
        ttk.Label(lang_frame, text="تنسيق التاريخ - Date Format:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.date_format_var = tk.StringVar()
        date_format_combo = ttk.Combobox(lang_frame, textvariable=self.date_format_var,
                                       values=[("gregorian", "Gregorian"), ("hijri", "Hijri")], state="readonly")
        date_format_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Auto backup
        backup_frame = ttk.LabelFrame(general_frame, text="النسخ الاحتياطي - Backup", padding="10")
        backup_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.auto_backup_var = tk.BooleanVar()
        ttk.Checkbutton(backup_frame, text="تفعيل النسخ الاحتياطي التلقائي - Enable Auto Backup",
                       variable=self.auto_backup_var).pack(anchor=tk.W)
        
        ttk.Label(backup_frame, text="فترة النسخ الاحتياطي (ساعات) - Backup Interval (hours):").pack(anchor=tk.W, pady=(10, 0))
        self.backup_interval_var = tk.StringVar()
        ttk.Entry(backup_frame, textvariable=self.backup_interval_var, width=10).pack(anchor=tk.W, pady=(5, 0))
        
    def create_shop_tab(self):
        """Create shop information tab"""
        shop_frame = ttk.Frame(self.notebook)
        self.notebook.add(shop_frame, text="معلومات المتجر - Shop Info")
        
        # Shop details
        details_frame = ttk.LabelFrame(shop_frame, text="تفاصيل المتجر - Shop Details", padding="10")
        details_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Arabic name
        ttk.Label(details_frame, text="اسم المتجر بالعربية - Shop Name (Arabic):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.shop_name_ar_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.shop_name_ar_var, width=40).grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # English name
        ttk.Label(details_frame, text="Shop Name (English):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.shop_name_en_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.shop_name_en_var, width=40).grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Address
        ttk.Label(details_frame, text="العنوان - Address:").grid(row=2, column=0, sticky=tk.W+tk.N, pady=5)
        self.address_ar_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.address_ar_var, width=40).grid(row=2, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Phone
        ttk.Label(details_frame, text="الهاتف - Phone:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.phone_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.phone_var, width=40).grid(row=3, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Email
        ttk.Label(details_frame, text="البريد الإلكتروني - Email:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.email_var, width=40).grid(row=4, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        details_frame.grid_columnconfigure(1, weight=1)
        
        # Legal information
        legal_frame = ttk.LabelFrame(shop_frame, text="المعلومات القانونية - Legal Information", padding="10")
        legal_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Commercial registration
        ttk.Label(legal_frame, text="السجل التجاري - Commercial Registration:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.commercial_reg_var = tk.StringVar()
        ttk.Entry(legal_frame, textvariable=self.commercial_reg_var, width=40).grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        # Tax number
        ttk.Label(legal_frame, text="الرقم الضريبي - Tax Number:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.tax_number_var = tk.StringVar()
        ttk.Entry(legal_frame, textvariable=self.tax_number_var, width=40).grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        
        legal_frame.grid_columnconfigure(1, weight=1)
        
        # Logo and stamp
        media_frame = ttk.LabelFrame(shop_frame, text="الشعار والختم - Logo & Stamp", padding="10")
        media_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Logo
        logo_row = ttk.Frame(media_frame)
        logo_row.pack(fill=tk.X, pady=5)
        
        ttk.Label(logo_row, text="الشعار - Logo:").pack(side=tk.LEFT)
        self.logo_path_var = tk.StringVar()
        ttk.Entry(logo_row, textvariable=self.logo_path_var, width=30).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(logo_row, text="تصفح - Browse", command=self.browse_logo).pack(side=tk.LEFT)
        
        # Stamp
        stamp_row = ttk.Frame(media_frame)
        stamp_row.pack(fill=tk.X, pady=5)
        
        ttk.Label(stamp_row, text="الختم - Stamp:").pack(side=tk.LEFT)
        self.stamp_path_var = tk.StringVar()
        ttk.Entry(stamp_row, textvariable=self.stamp_path_var, width=30).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(stamp_row, text="تصفح - Browse", command=self.browse_stamp).pack(side=tk.LEFT)
        
    def create_business_tab(self):
        """Create business settings tab"""
        business_frame = ttk.Frame(self.notebook)
        self.notebook.add(business_frame, text="الأعمال - Business")
        
        # Currency settings
        currency_frame = ttk.LabelFrame(business_frame, text="العملة - Currency", padding="10")
        currency_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(currency_frame, text="رمز العملة - Currency Code:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.currency_code_var = tk.StringVar()
        ttk.Entry(currency_frame, textvariable=self.currency_code_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(currency_frame, text="رمز العملة - Currency Symbol:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.currency_symbol_var = tk.StringVar()
        ttk.Entry(currency_frame, textvariable=self.currency_symbol_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Tax settings
        tax_frame = ttk.LabelFrame(business_frame, text="الضرائب - Tax", padding="10")
        tax_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(tax_frame, text="معدل ضريبة القيمة المضافة (%) - VAT Rate (%):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.vat_rate_var = tk.StringVar()
        ttk.Entry(tax_frame, textvariable=self.vat_rate_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Invoice settings
        invoice_frame = ttk.LabelFrame(business_frame, text="الفواتير - Invoices", padding="10")
        invoice_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(invoice_frame, text="بادئة رقم الفاتورة - Invoice Prefix:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.invoice_prefix_var = tk.StringVar()
        ttk.Entry(invoice_frame, textvariable=self.invoice_prefix_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(invoice_frame, text="بادئة رقم الإيصال - Receipt Prefix:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.receipt_prefix_var = tk.StringVar()
        ttk.Entry(invoice_frame, textvariable=self.receipt_prefix_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
    def create_hardware_tab(self):
        """Create hardware settings tab"""
        hardware_frame = ttk.Frame(self.notebook)
        self.notebook.add(hardware_frame, text="الأجهزة - Hardware")
        
        # Printer settings
        printer_frame = ttk.LabelFrame(hardware_frame, text="الطابعة - Printer", padding="10")
        printer_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.thermal_printer_var = tk.BooleanVar()
        ttk.Checkbutton(printer_frame, text="تفعيل الطابعة الحرارية - Enable Thermal Printer",
                       variable=self.thermal_printer_var).pack(anchor=tk.W)
        
        ttk.Label(printer_frame, text="منفذ الطابعة - Printer Port:").pack(anchor=tk.W, pady=(10, 0))
        self.printer_port_var = tk.StringVar()
        printer_port_combo = ttk.Combobox(printer_frame, textvariable=self.printer_port_var,
                                        values=["COM1", "COM2", "COM3", "USB", "Network"])
        printer_port_combo.pack(anchor=tk.W, pady=(5, 0))
        
        # Barcode scanner settings
        scanner_frame = ttk.LabelFrame(hardware_frame, text="قارئ الباركود - Barcode Scanner", padding="10")
        scanner_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.barcode_scanner_var = tk.BooleanVar()
        ttk.Checkbutton(scanner_frame, text="تفعيل قارئ الباركود - Enable Barcode Scanner",
                       variable=self.barcode_scanner_var).pack(anchor=tk.W)
        
        ttk.Label(scanner_frame, text="منفذ القارئ - Scanner Port:").pack(anchor=tk.W, pady=(10, 0))
        self.scanner_port_var = tk.StringVar()
        scanner_port_combo = ttk.Combobox(scanner_frame, textvariable=self.scanner_port_var,
                                        values=["COM1", "COM2", "COM3", "USB"])
        scanner_port_combo.pack(anchor=tk.W, pady=(5, 0))
        
        # Cash drawer settings
        drawer_frame = ttk.LabelFrame(hardware_frame, text="درج النقد - Cash Drawer", padding="10")
        drawer_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.cash_drawer_var = tk.BooleanVar()
        ttk.Checkbutton(drawer_frame, text="تفعيل درج النقد - Enable Cash Drawer",
                       variable=self.cash_drawer_var).pack(anchor=tk.W)
        
    def create_appearance_tab(self):
        """Create appearance settings tab"""
        appearance_frame = ttk.Frame(self.notebook)
        self.notebook.add(appearance_frame, text="المظهر - Appearance")
        
        # Theme settings
        theme_frame = ttk.LabelFrame(appearance_frame, text="المظهر - Theme", padding="10")
        theme_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(theme_frame, text="نمط المظهر - Theme Style:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.theme_var = tk.StringVar()
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var,
                                 values=["light", "dark", "auto"], state="readonly")
        theme_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Font settings
        font_frame = ttk.LabelFrame(appearance_frame, text="الخط - Font", padding="10")
        font_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(font_frame, text="حجم الخط - Font Size:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.font_size_var = tk.StringVar()
        font_size_combo = ttk.Combobox(font_frame, textvariable=self.font_size_var,
                                     values=["8", "9", "10", "11", "12", "14", "16"])
        font_size_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
    def create_backup_tab(self):
        """Create backup settings tab"""
        backup_frame = ttk.Frame(self.notebook)
        self.notebook.add(backup_frame, text="النسخ الاحتياطي - Backup")
        
        # Backup location
        location_frame = ttk.LabelFrame(backup_frame, text="موقع النسخ الاحتياطي - Backup Location", padding="10")
        location_frame.pack(fill=tk.X, padx=10, pady=5)
        
        backup_row = ttk.Frame(location_frame)
        backup_row.pack(fill=tk.X, pady=5)
        
        ttk.Label(backup_row, text="مجلد النسخ الاحتياطي - Backup Folder:").pack(side=tk.LEFT)
        self.backup_path_var = tk.StringVar()
        ttk.Entry(backup_row, textvariable=self.backup_path_var, width=40).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(backup_row, text="تصفح - Browse", command=self.browse_backup_folder).pack(side=tk.LEFT)
        
        # Backup actions
        actions_frame = ttk.LabelFrame(backup_frame, text="إجراءات النسخ الاحتياطي - Backup Actions", padding="10")
        actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(actions_frame, text="إنشاء نسخة احتياطية الآن - Create Backup Now",
                  command=self.create_backup).pack(pady=5)
        ttk.Button(actions_frame, text="استعادة من نسخة احتياطية - Restore from Backup",
                  command=self.restore_backup).pack(pady=5)
        
    def create_buttons(self):
        """Create action buttons"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="حفظ - Save", command=self.save_settings).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء - Cancel", command=self.cancel).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="استعادة الافتراضي - Reset to Default", command=self.reset_defaults).pack(side=tk.LEFT)

    def load_settings(self):
        """Load current settings"""
        try:
            # General settings
            self.language_var.set(self.settings.get('language', 'APP', 'ar'))
            self.date_format_var.set(self.settings.get('date_format', 'BUSINESS', 'hijri'))
            self.auto_backup_var.set(self.settings.get_bool('auto_backup', 'APP', True))
            self.backup_interval_var.set(self.settings.get('backup_interval', 'APP', '24'))

            # Shop information
            shop_config = self.settings.load_shop_config()
            self.shop_name_ar_var.set(shop_config.get('shop_name_ar', ''))
            self.shop_name_en_var.set(shop_config.get('shop_name_en', ''))
            self.address_ar_var.set(shop_config.get('address_ar', ''))
            self.phone_var.set(shop_config.get('phone', ''))
            self.email_var.set(shop_config.get('email', ''))
            self.commercial_reg_var.set(shop_config.get('commercial_registration', ''))
            self.tax_number_var.set(shop_config.get('tax_number', ''))
            self.logo_path_var.set(shop_config.get('logo_path', ''))
            self.stamp_path_var.set(shop_config.get('stamp_path', ''))

            # Business settings
            self.currency_code_var.set(self.settings.get('currency_code', 'BUSINESS', 'QAR'))
            self.currency_symbol_var.set(shop_config.get('currency_symbol', 'ر.ق'))
            self.vat_rate_var.set(self.settings.get('vat_rate', 'BUSINESS', '0.0'))
            self.invoice_prefix_var.set(self.settings.get('invoice_prefix', 'BUSINESS', 'INV'))
            self.receipt_prefix_var.set(self.settings.get('receipt_prefix', 'BUSINESS', 'RCP'))

            # Hardware settings
            self.thermal_printer_var.set(self.settings.get_bool('thermal_printer', 'HARDWARE', False))
            self.printer_port_var.set(self.settings.get('printer_port', 'HARDWARE', 'COM1'))
            self.barcode_scanner_var.set(self.settings.get_bool('barcode_scanner', 'HARDWARE', False))
            self.scanner_port_var.set(self.settings.get('scanner_port', 'HARDWARE', 'COM2'))
            self.cash_drawer_var.set(self.settings.get_bool('cash_drawer', 'HARDWARE', False))

            # Appearance settings
            self.theme_var.set(self.settings.get('theme', 'APP', 'light'))
            self.font_size_var.set(self.settings.get('font_size', 'APP', '10'))

            # Backup settings
            self.backup_path_var.set(self.settings.get('backup_path', 'DATABASE', 'database/backups/'))

        except Exception as e:
            self.logger.error(f"Error loading settings: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في تحميل الإعدادات\nError loading settings: {str(e)}")

    def save_settings(self):
        """Save all settings"""
        try:
            if not auth_manager.can_access_feature('system_settings'):
                messagebox.showerror("خطأ - Error", "غير مصرح لك بتعديل إعدادات النظام\nNot authorized to modify system settings")
                return

            # Save general settings
            self.settings.set('language', self.language_var.get(), 'APP')
            self.settings.set('date_format', self.date_format_var.get(), 'BUSINESS')
            self.settings.set('auto_backup', str(self.auto_backup_var.get()), 'APP')
            self.settings.set('backup_interval', self.backup_interval_var.get(), 'APP')

            # Save business settings
            self.settings.set('currency_code', self.currency_code_var.get(), 'BUSINESS')
            self.settings.set('vat_rate', self.vat_rate_var.get(), 'BUSINESS')
            self.settings.set('invoice_prefix', self.invoice_prefix_var.get(), 'BUSINESS')
            self.settings.set('receipt_prefix', self.receipt_prefix_var.get(), 'BUSINESS')

            # Save hardware settings
            self.settings.set('thermal_printer', str(self.thermal_printer_var.get()), 'HARDWARE')
            self.settings.set('printer_port', self.printer_port_var.get(), 'HARDWARE')
            self.settings.set('barcode_scanner', str(self.barcode_scanner_var.get()), 'HARDWARE')
            self.settings.set('scanner_port', self.scanner_port_var.get(), 'HARDWARE')
            self.settings.set('cash_drawer', str(self.cash_drawer_var.get()), 'HARDWARE')

            # Save appearance settings
            self.settings.set('theme', self.theme_var.get(), 'APP')
            self.settings.set('font_size', self.font_size_var.get(), 'APP')

            # Save backup settings
            self.settings.set('backup_path', self.backup_path_var.get(), 'DATABASE')

            # Save shop configuration
            shop_config = {
                'shop_name_ar': self.shop_name_ar_var.get(),
                'shop_name_en': self.shop_name_en_var.get(),
                'address_ar': self.address_ar_var.get(),
                'address_en': self.address_ar_var.get(),  # Using same for now
                'phone': self.phone_var.get(),
                'email': self.email_var.get(),
                'commercial_registration': self.commercial_reg_var.get(),
                'tax_number': self.tax_number_var.get(),
                'logo_path': self.logo_path_var.get(),
                'stamp_path': self.stamp_path_var.get(),
                'currency_symbol': self.currency_symbol_var.get(),
                'currency_code': self.currency_code_var.get()
            }

            self.settings.save_shop_config(shop_config)

            messagebox.showinfo("نجح - Success", "تم حفظ الإعدادات بنجاح\nSettings saved successfully")
            self.window.destroy()

        except Exception as e:
            self.logger.error(f"Error saving settings: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في حفظ الإعدادات\nError saving settings: {str(e)}")

    def browse_logo(self):
        """Browse for logo file"""
        filename = filedialog.askopenfilename(
            title="اختر شعار المتجر - Select Shop Logo",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            # Copy to assets folder
            assets_dir = Path("assets")
            assets_dir.mkdir(exist_ok=True)

            logo_path = assets_dir / "logo.png"
            try:
                shutil.copy2(filename, logo_path)
                self.logo_path_var.set(str(logo_path))
            except Exception as e:
                self.logger.error(f"Error copying logo: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في نسخ الشعار\nError copying logo: {str(e)}")

    def browse_stamp(self):
        """Browse for stamp file"""
        filename = filedialog.askopenfilename(
            title="اختر ختم المتجر - Select Shop Stamp",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            # Copy to assets folder
            assets_dir = Path("assets")
            assets_dir.mkdir(exist_ok=True)

            stamp_path = assets_dir / "stamp.png"
            try:
                shutil.copy2(filename, stamp_path)
                self.stamp_path_var.set(str(stamp_path))
            except Exception as e:
                self.logger.error(f"Error copying stamp: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في نسخ الختم\nError copying stamp: {str(e)}")

    def browse_backup_folder(self):
        """Browse for backup folder"""
        folder = filedialog.askdirectory(title="اختر مجلد النسخ الاحتياطي - Select Backup Folder")
        if folder:
            self.backup_path_var.set(folder)

    def create_backup(self):
        """Create database backup"""
        try:
            backup_path = Path(self.backup_path_var.get())
            backup_path.mkdir(parents=True, exist_ok=True)

            # Create backup filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = backup_path / f"pos_backup_{timestamp}.db"

            # Copy database file
            db_path = Path("database/pos_system.db")
            if db_path.exists():
                shutil.copy2(db_path, backup_file)
                messagebox.showinfo("نجح - Success", f"تم إنشاء النسخة الاحتياطية\nBackup created: {backup_file}")
            else:
                messagebox.showerror("خطأ - Error", "ملف قاعدة البيانات غير موجود\nDatabase file not found")

        except Exception as e:
            self.logger.error(f"Error creating backup: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في إنشاء النسخة الاحتياطية\nError creating backup: {str(e)}")

    def restore_backup(self):
        """Restore from backup"""
        filename = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية - Select Backup File",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )

        if filename:
            if messagebox.askyesno("تأكيد - Confirm",
                                  "هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.\n"
                                  "Are you sure you want to restore from backup? Current data will be replaced."):
                try:
                    db_path = Path("database/pos_system.db")
                    shutil.copy2(filename, db_path)
                    messagebox.showinfo("نجح - Success", "تم استعادة النسخة الاحتياطية بنجاح\nBackup restored successfully")
                except Exception as e:
                    self.logger.error(f"Error restoring backup: {e}")
                    messagebox.showerror("خطأ - Error", f"خطأ في استعادة النسخة الاحتياطية\nError restoring backup: {str(e)}")

    def reset_defaults(self):
        """Reset all settings to defaults"""
        if messagebox.askyesno("تأكيد - Confirm",
                              "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟\n"
                              "Are you sure you want to reset all settings to defaults?"):
            try:
                # Reset to default values
                self.language_var.set('ar')
                self.date_format_var.set('hijri')
                self.auto_backup_var.set(True)
                self.backup_interval_var.set('24')

                self.shop_name_ar_var.set('متجر قطر')
                self.shop_name_en_var.set('Qatar Store')
                self.address_ar_var.set('الدوحة، قطر')
                self.phone_var.set('+974 XXXX XXXX')
                self.email_var.set('<EMAIL>')
                self.commercial_reg_var.set('CR-XXXXXXX')
                self.tax_number_var.set('TAX-XXXXXXX')
                self.logo_path_var.set('assets/logo.png')
                self.stamp_path_var.set('assets/stamp.png')

                self.currency_code_var.set('QAR')
                self.currency_symbol_var.set('ر.ق')
                self.vat_rate_var.set('0.0')
                self.invoice_prefix_var.set('INV')
                self.receipt_prefix_var.set('RCP')

                self.thermal_printer_var.set(False)
                self.printer_port_var.set('COM1')
                self.barcode_scanner_var.set(False)
                self.scanner_port_var.set('COM2')
                self.cash_drawer_var.set(False)

                self.theme_var.set('light')
                self.font_size_var.set('10')

                self.backup_path_var.set('database/backups/')

                messagebox.showinfo("نجح - Success", "تم إعادة تعيين الإعدادات للقيم الافتراضية\nSettings reset to defaults")

            except Exception as e:
                self.logger.error(f"Error resetting settings: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في إعادة تعيين الإعدادات\nError resetting settings: {str(e)}")

    def cancel(self):
        """Cancel and close window"""
        self.window.destroy()

    def run(self):
        """Run the settings window"""
        if self.window:
            self.window.mainloop()
