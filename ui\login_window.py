#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Login window for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import os
from pathlib import Path

from core.auth import auth_manager
from config.settings import Settings
from utils.logger import get_logger

class LoginWindow:
    """Login window class"""
    
    def __init__(self):
        self.settings = Settings()
        self.logger = get_logger()
        self.root = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the login user interface"""
        self.root = tk.Tk()
        self.root.title("Qatar POS System - نظام نقاط البيع القطري")
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Logo section
        self.create_logo_section(main_frame)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="نظام نقاط البيع القطري\nQatar POS System",
            font=("Arial", 16, "bold"),
            justify=tk.CENTER
        )
        title_label.pack(pady=(0, 30))
        
        # Login form
        self.create_login_form(main_frame)
        
        # Language selection
        self.create_language_section(main_frame)
        
        # Footer
        footer_label = ttk.Label(
            main_frame,
            text="© 2025 Qatar POS System",
            font=("Arial", 8),
            foreground="gray"
        )
        footer_label.pack(side=tk.BOTTOM, pady=(20, 0))
        
    def create_logo_section(self, parent):
        """Create logo section"""
        logo_frame = ttk.Frame(parent)
        logo_frame.pack(pady=(0, 20))
        
        # Try to load logo
        logo_path = Path("assets/logo.png")
        if logo_path.exists():
            try:
                image = Image.open(logo_path)
                image = image.resize((80, 80), Image.Resampling.LANCZOS)
                self.logo_image = ImageTk.PhotoImage(image)
                
                logo_label = ttk.Label(logo_frame, image=self.logo_image)
                logo_label.pack()
            except Exception as e:
                self.logger.warning(f"Could not load logo: {e}")
                self.create_default_logo(logo_frame)
        else:
            self.create_default_logo(logo_frame)
            
    def create_default_logo(self, parent):
        """Create default logo placeholder"""
        logo_label = ttk.Label(
            parent,
            text="🏪",
            font=("Arial", 48)
        )
        logo_label.pack()
        
    def create_login_form(self, parent):
        """Create login form"""
        form_frame = ttk.LabelFrame(parent, text="تسجيل الدخول - Login", padding="20")
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Username field
        ttk.Label(form_frame, text="اسم المستخدم - Username:").pack(anchor=tk.W, pady=(0, 5))
        self.username_var = tk.StringVar(value="admin")
        self.username_entry = ttk.Entry(form_frame, textvariable=self.username_var, font=("Arial", 12))
        self.username_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Password field
        ttk.Label(form_frame, text="كلمة المرور - Password:").pack(anchor=tk.W, pady=(0, 5))
        self.password_var = tk.StringVar(value="admin123")
        self.password_entry = ttk.Entry(form_frame, textvariable=self.password_var, show="*", font=("Arial", 12))
        self.password_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Remember me checkbox
        self.remember_var = tk.BooleanVar()
        remember_cb = ttk.Checkbutton(
            form_frame, 
            text="تذكرني - Remember me",
            variable=self.remember_var
        )
        remember_cb.pack(anchor=tk.W, pady=(0, 15))
        
        # Login button
        login_btn = ttk.Button(
            form_frame,
            text="دخول - Login",
            command=self.login,
            style="Accent.TButton"
        )
        login_btn.pack(fill=tk.X, pady=(0, 10))
        
        # Bind Enter key to login
        self.root.bind('<Return>', lambda event: self.login())
        
        # Focus on username field
        self.username_entry.focus()
        
    def create_language_section(self, parent):
        """Create language selection section"""
        lang_frame = ttk.Frame(parent)
        lang_frame.pack(fill=tk.X)
        
        ttk.Label(lang_frame, text="اللغة - Language:").pack(side=tk.LEFT)
        
        self.language_var = tk.StringVar(value=self.settings.get('language', 'ar'))
        language_combo = ttk.Combobox(
            lang_frame,
            textvariable=self.language_var,
            values=[("ar", "العربية"), ("en", "English")],
            state="readonly",
            width=15
        )
        language_combo.pack(side=tk.RIGHT)
        language_combo.bind('<<ComboboxSelected>>', self.change_language)
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def login(self):
        """Handle login attempt"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username or not password:
            messagebox.showerror(
                "خطأ - Error",
                "يرجى إدخال اسم المستخدم وكلمة المرور\nPlease enter username and password"
            )
            return
            
        # Show loading cursor
        self.root.config(cursor="wait")
        self.root.update()
        
        try:
            if auth_manager.authenticate(username, password):
                self.logger.info(f"Successful login for user: {username}")
                
                # Save remember me preference
                if self.remember_var.get():
                    self.settings.set('last_username', username)
                else:
                    self.settings.set('last_username', '')
                    
                # Close login window and open main application
                self.root.destroy()
                self.open_main_application()
                
            else:
                messagebox.showerror(
                    "خطأ في تسجيل الدخول - Login Error",
                    "اسم المستخدم أو كلمة المرور غير صحيحة\nInvalid username or password"
                )
                self.password_entry.delete(0, tk.END)
                self.password_entry.focus()
                
        except Exception as e:
            self.logger.error(f"Login error: {e}")
            messagebox.showerror(
                "خطأ - Error",
                f"حدث خطأ أثناء تسجيل الدخول\nLogin error occurred: {str(e)}"
            )
        finally:
            self.root.config(cursor="")
            
    def change_language(self, event=None):
        """Handle language change"""
        language = self.language_var.get()
        self.settings.set('language', language)
        
        # Update UI text based on language
        # This would be implemented with proper localization
        self.logger.info(f"Language changed to: {language}")
        
    def open_main_application(self):
        """Open the main POS application"""
        try:
            from ui.main_window import MainWindow
            app = MainWindow()
            app.run()
        except ImportError:
            # Close login window and open main system
            self.root.destroy()

            # Import and run main window
            try:
                from ui.main_window import MainWindow
                main_app = MainWindow()
                main_app.run()
            except Exception as e:
                self.logger.error(f"Error opening main window: {e}")
                messagebox.showerror(
                    "خطأ - Error",
                    f"فشل في فتح النظام الرئيسي\nFailed to open main system:\n{str(e)}"
                )
            
    def run(self):
        """Run the login window"""
        self.root.mainloop()
