#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Customers management window for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

from core.customer_manager import CustomerManager
from core.auth import auth_manager
from database.models import Customer
from config.settings import Settings
from utils.logger import get_logger

class CustomersWindow:
    """Customers management window"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.customer_manager = CustomerManager()
        self.settings = Settings()
        self.logger = get_logger()
        
        self.window = None
        self.selected_customer_id = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the customers management interface"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("إدارة العملاء - Customers Management")
        self.window.geometry("1000x700")
        
        # Create main layout
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
        
        # Load initial data
        self.refresh_customers()
        
    def create_toolbar(self):
        """Create toolbar with action buttons"""
        toolbar = ttk.Frame(self.window)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # Customer actions
        ttk.Button(toolbar, text="عميل جديد - New Customer", 
                  command=self.new_customer).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تعديل - Edit", 
                  command=self.edit_customer).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="حذف - Delete", 
                  command=self.delete_customer).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Credit management
        ttk.Button(toolbar, text="تحديث الرصيد - Update Balance", 
                  command=self.update_balance).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تقرير الديون - Credit Report", 
                  command=self.credit_report).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Import/Export
        ttk.Button(toolbar, text="استيراد - Import", 
                  command=self.import_customers).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تصدير - Export", 
                  command=self.export_customers).pack(side=tk.LEFT, padx=(0, 5))
        
        # Refresh button
        ttk.Button(toolbar, text="تحديث - Refresh", 
                  command=self.refresh_customers).pack(side=tk.RIGHT)
        
    def create_main_content(self):
        """Create main content area"""
        # Create paned window for resizable layout
        paned = ttk.PanedWindow(self.window, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Customers list
        left_frame = ttk.Frame(paned)
        paned.add(left_frame, weight=2)
        
        self.create_customers_list(left_frame)
        
        # Right panel - Customer details
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)
        
        self.create_customer_details(right_frame)
        
    def create_customers_list(self, parent):
        """Create customers list with search"""
        # Search section
        search_frame = ttk.LabelFrame(parent, text="البحث - Search", padding="10")
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Search entry
        search_row = ttk.Frame(search_frame)
        search_row.pack(fill=tk.X)
        
        ttk.Label(search_row, text="البحث - Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_row, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 5))
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        ttk.Button(search_row, text="بحث - Search", 
                  command=self.search_customers).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_row, text="مسح - Clear", 
                  command=self.clear_search).pack(side=tk.LEFT)
        
        # Customers list
        list_frame = ttk.LabelFrame(parent, text="قائمة العملاء - Customers List", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview for customers
        columns = ('name', 'phone', 'email', 'balance', 'total_purchases', 'last_purchase')
        self.customers_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.customers_tree.heading('name', text='الاسم - Name')
        self.customers_tree.heading('phone', text='الهاتف - Phone')
        self.customers_tree.heading('email', text='البريد - Email')
        self.customers_tree.heading('balance', text='الرصيد - Balance')
        self.customers_tree.heading('total_purchases', text='إجمالي المشتريات')
        self.customers_tree.heading('last_purchase', text='آخر شراء')
        
        self.customers_tree.column('name', width=150)
        self.customers_tree.column('phone', width=120)
        self.customers_tree.column('email', width=150)
        self.customers_tree.column('balance', width=100, anchor=tk.CENTER)
        self.customers_tree.column('total_purchases', width=120, anchor=tk.CENTER)
        self.customers_tree.column('last_purchase', width=100, anchor=tk.CENTER)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.customers_tree.xview)
        self.customers_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.customers_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # Bind selection event
        self.customers_tree.bind('<<TreeviewSelect>>', self.on_customer_select)
        self.customers_tree.bind('<Double-1>', self.edit_customer)
        
    def create_customer_details(self, parent):
        """Create customer details panel"""
        details_frame = ttk.LabelFrame(parent, text="تفاصيل العميل - Customer Details", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # Customer info
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Create info labels
        self.info_labels = {}
        info_fields = [
            ('name', 'الاسم - Name'),
            ('phone', 'الهاتف - Phone'),
            ('email', 'البريد - Email'),
            ('national_id', 'الهوية - National ID'),
            ('address', 'العنوان - Address'),
            ('credit_limit', 'حد الائتمان - Credit Limit'),
            ('current_balance', 'الرصيد الحالي - Current Balance'),
            ('created_at', 'تاريخ التسجيل - Registration Date')
        ]
        
        for i, (field, label) in enumerate(info_fields):
            ttk.Label(info_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.info_labels[field] = ttk.Label(info_frame, text="-", relief=tk.SUNKEN, width=25)
            self.info_labels[field].grid(row=i, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=2)
        
        info_frame.grid_columnconfigure(1, weight=1)
        
        # Statistics
        stats_frame = ttk.LabelFrame(details_frame, text="الإحصائيات - Statistics", padding="5")
        stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.stats_labels = {}
        stats_fields = [
            ('total_orders', 'إجمالي الطلبات - Total Orders'),
            ('total_spent', 'إجمالي المبلغ - Total Spent'),
            ('avg_order_value', 'متوسط قيمة الطلب - Avg Order Value'),
            ('last_purchase_date', 'آخر شراء - Last Purchase')
        ]
        
        for i, (field, label) in enumerate(stats_fields):
            ttk.Label(stats_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.stats_labels[field] = ttk.Label(stats_frame, text="-", relief=tk.SUNKEN, width=25)
            self.stats_labels[field].grid(row=i, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=2)
        
        stats_frame.grid_columnconfigure(1, weight=1)
        
        # Purchase history
        history_frame = ttk.LabelFrame(details_frame, text="تاريخ المشتريات - Purchase History", padding="5")
        history_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # History treeview
        history_columns = ('date', 'invoice', 'amount', 'payment_method')
        self.history_tree = ttk.Treeview(history_frame, columns=history_columns, 
                                       show='headings', height=8)
        
        self.history_tree.heading('date', text='التاريخ - Date')
        self.history_tree.heading('invoice', text='الفاتورة - Invoice')
        self.history_tree.heading('amount', text='المبلغ - Amount')
        self.history_tree.heading('payment_method', text='طريقة الدفع - Payment')
        
        self.history_tree.column('date', width=100)
        self.history_tree.column('invoice', width=100)
        self.history_tree.column('amount', width=80, anchor=tk.CENTER)
        self.history_tree.column('payment_method', width=80, anchor=tk.CENTER)
        
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, 
                                        command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_status_bar(self):
        """Create status bar"""
        status_frame = ttk.Frame(self.window)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar(value="جاهز - Ready")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # Customers count
        self.customers_count_var = tk.StringVar(value="العملاء: 0")
        ttk.Label(status_frame, textvariable=self.customers_count_var).pack(side=tk.RIGHT, padx=5)

    def refresh_customers(self):
        """Refresh customers list"""
        try:
            # Clear existing items
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # Get search query
            search_query = self.search_var.get().strip()

            # Get customers
            if search_query:
                customers = self.customer_manager.search_customers(search_query)
            else:
                customers = self.get_all_customers()

            # Populate treeview
            for customer in customers:
                # Get customer statistics
                stats = self.customer_manager.get_customer_statistics(customer['id'])

                # Format last purchase date
                last_purchase = stats.get('last_purchase_date', '')
                if last_purchase:
                    try:
                        last_purchase = last_purchase[:10]  # YYYY-MM-DD
                    except:
                        last_purchase = '-'
                else:
                    last_purchase = '-'

                values = (
                    customer['name'],
                    customer['phone'] or '-',
                    customer['email'] or '-',
                    f"{customer['current_balance']:.2f}",
                    f"{stats.get('total_spent', 0):.2f}",
                    last_purchase
                )

                # Color coding for customers with credit balance
                tags = []
                if customer['current_balance'] > 0:
                    tags = ['has_credit']

                item = self.customers_tree.insert('', tk.END, values=values, tags=tags)
                # Store customer ID in item
                self.customers_tree.set(item, '#0', customer['id'])

            # Configure tag colors
            self.customers_tree.tag_configure('has_credit', background='#fff3cd')

            # Update customers count
            self.customers_count_var.set(f"العملاء: {len(customers)}")
            self.status_var.set("تم تحديث قائمة العملاء - Customers list updated")

        except Exception as e:
            self.logger.error(f"Error refreshing customers: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في تحديث العملاء\nError refreshing customers: {str(e)}")

    def get_all_customers(self):
        """Get all active customers"""
        try:
            with self.customer_manager.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM customers
                    WHERE is_active = 1
                    ORDER BY name
                """)

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"Error getting all customers: {e}")
            return []

    def on_search_change(self, event=None):
        """Handle search text change"""
        # Auto-search after a short delay
        if hasattr(self, '_search_timer'):
            self.window.after_cancel(self._search_timer)
        self._search_timer = self.window.after(500, self.search_customers)

    def search_customers(self):
        """Search customers"""
        self.refresh_customers()

    def clear_search(self):
        """Clear search"""
        self.search_var.set("")
        self.refresh_customers()

    def on_customer_select(self, event=None):
        """Handle customer selection"""
        selection = self.customers_tree.selection()
        if not selection:
            self.clear_customer_details()
            return

        item = selection[0]
        customer_id = self.customers_tree.set(item, '#0')

        try:
            customer_id = int(customer_id)
            self.selected_customer_id = customer_id
            self.load_customer_details(customer_id)

        except (ValueError, TypeError):
            self.clear_customer_details()

    def load_customer_details(self, customer_id):
        """Load customer details"""
        try:
            customer = self.customer_manager.get_customer_by_id(customer_id)
            if not customer:
                self.clear_customer_details()
                return

            # Update info labels
            self.info_labels['name'].config(text=customer['name'])
            self.info_labels['phone'].config(text=customer['phone'] or '-')
            self.info_labels['email'].config(text=customer['email'] or '-')
            self.info_labels['national_id'].config(text=customer['national_id'] or '-')
            self.info_labels['address'].config(text=customer['address'] or '-')
            self.info_labels['credit_limit'].config(text=f"{customer['credit_limit']:.2f} QAR")
            self.info_labels['current_balance'].config(text=f"{customer['current_balance']:.2f} QAR")

            created_at = customer['created_at'][:10] if customer['created_at'] else '-'
            self.info_labels['created_at'].config(text=created_at)

            # Load statistics
            self.load_customer_statistics(customer_id)

            # Load purchase history
            self.load_purchase_history(customer_id)

        except Exception as e:
            self.logger.error(f"Error loading customer details: {e}")

    def clear_customer_details(self):
        """Clear customer details"""
        self.selected_customer_id = None

        for label in self.info_labels.values():
            label.config(text="-")

        for label in self.stats_labels.values():
            label.config(text="-")

        # Clear history
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

    def load_customer_statistics(self, customer_id):
        """Load customer statistics"""
        try:
            stats = self.customer_manager.get_customer_statistics(customer_id)

            self.stats_labels['total_orders'].config(text=str(stats.get('total_orders', 0)))
            self.stats_labels['total_spent'].config(text=f"{stats.get('total_spent', 0):.2f} QAR")
            self.stats_labels['avg_order_value'].config(text=f"{stats.get('avg_order_value', 0):.2f} QAR")

            last_purchase = stats.get('last_purchase_date', '')
            if last_purchase:
                try:
                    last_purchase = last_purchase[:10]  # YYYY-MM-DD
                except:
                    last_purchase = '-'
            else:
                last_purchase = '-'
            self.stats_labels['last_purchase_date'].config(text=last_purchase)

        except Exception as e:
            self.logger.error(f"Error loading customer statistics: {e}")

    def load_purchase_history(self, customer_id):
        """Load customer purchase history"""
        try:
            # Clear existing history
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)

            history = self.customer_manager.get_customer_purchase_history(customer_id, 20)

            for purchase in history:
                date_str = purchase['created_at'][:10] if purchase['created_at'] else '-'
                invoice_number = purchase['invoice_number']
                amount = f"{purchase['total_amount']:.2f}"
                payment_method = purchase['payment_method']

                self.history_tree.insert('', tk.END, values=(date_str, invoice_number, amount, payment_method))

        except Exception as e:
            self.logger.error(f"Error loading purchase history: {e}")

    def new_customer(self):
        """Create new customer"""
        if not auth_manager.can_access_feature('customers'):
            messagebox.showerror("خطأ - Error", "غير مصرح لك بإدارة العملاء\nNot authorized for customer management")
            return

        from ui.customer_dialogs import CustomerDialog
        dialog = CustomerDialog(self.window, "عميل جديد - New Customer")
        if dialog.result:
            try:
                customer = Customer(**dialog.result)
                customer_id = self.customer_manager.create_customer(customer)
                if customer_id:
                    messagebox.showinfo("نجح - Success", "تم إنشاء العميل بنجاح\nCustomer created successfully")
                    self.refresh_customers()
                else:
                    messagebox.showerror("خطأ - Error", "فشل في إنشاء العميل\nFailed to create customer")
            except Exception as e:
                self.logger.error(f"Error creating customer: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في إنشاء العميل\nError creating customer: {str(e)}")

    def edit_customer(self):
        """Edit selected customer"""
        if not self.selected_customer_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار عميل للتعديل\nPlease select a customer to edit")
            return

        if not auth_manager.can_access_feature('customers'):
            messagebox.showerror("خطأ - Error", "غير مصرح لك بإدارة العملاء\nNot authorized for customer management")
            return

        try:
            customer = self.customer_manager.get_customer_by_id(self.selected_customer_id)
            if not customer:
                messagebox.showerror("خطأ - Error", "العميل غير موجود\nCustomer not found")
                return

            from ui.customer_dialogs import CustomerDialog
            dialog = CustomerDialog(self.window, "تعديل العميل - Edit Customer", customer)
            if dialog.result:
                if self.customer_manager.update_customer(self.selected_customer_id, dialog.result):
                    messagebox.showinfo("نجح - Success", "تم تحديث العميل بنجاح\nCustomer updated successfully")
                    self.refresh_customers()
                    self.load_customer_details(self.selected_customer_id)
                else:
                    messagebox.showerror("خطأ - Error", "فشل في تحديث العميل\nFailed to update customer")

        except Exception as e:
            self.logger.error(f"Error editing customer: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في تعديل العميل\nError editing customer: {str(e)}")

    def delete_customer(self):
        """Delete selected customer"""
        if not self.selected_customer_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار عميل للحذف\nPlease select a customer to delete")
            return

        if not auth_manager.can_access_feature('customers'):
            messagebox.showerror("خطأ - Error", "غير مصرح لك بإدارة العملاء\nNot authorized for customer management")
            return

        if messagebox.askyesno("تأكيد الحذف - Confirm Delete",
                              "هل أنت متأكد من حذف هذا العميل؟\nAre you sure you want to delete this customer?"):
            try:
                # Soft delete by setting is_active to False
                if self.customer_manager.update_customer(self.selected_customer_id, {'is_active': False}):
                    messagebox.showinfo("نجح - Success", "تم حذف العميل بنجاح\nCustomer deleted successfully")
                    self.refresh_customers()
                    self.clear_customer_details()
                else:
                    messagebox.showerror("خطأ - Error", "فشل في حذف العميل\nFailed to delete customer")

            except Exception as e:
                self.logger.error(f"Error deleting customer: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في حذف العميل\nError deleting customer: {str(e)}")

    # Placeholder methods for additional features
    def update_balance(self):
        """Update customer balance"""
        if not self.selected_customer_id:
            messagebox.showwarning("تحذير - Warning", "يرجى اختيار عميل\nPlease select a customer")
            return

        from ui.customer_dialogs import BalanceUpdateDialog
        dialog = BalanceUpdateDialog(self.window, self.selected_customer_id)
        if dialog.result:
            try:
                amount = dialog.result['amount']
                operation = dialog.result['operation']

                if self.customer_manager.update_customer_balance(self.selected_customer_id, amount, operation):
                    messagebox.showinfo("نجح - Success", "تم تحديث الرصيد بنجاح\nBalance updated successfully")
                    self.refresh_customers()
                    self.load_customer_details(self.selected_customer_id)
                else:
                    messagebox.showerror("خطأ - Error", "فشل في تحديث الرصيد\nFailed to update balance")

            except Exception as e:
                self.logger.error(f"Error updating balance: {e}")
                messagebox.showerror("خطأ - Error", f"خطأ في تحديث الرصيد\nError updating balance: {str(e)}")

    def credit_report(self):
        """Show credit report"""
        messagebox.showinfo("قريباً - Coming Soon", "تقرير الديون قيد التطوير\nCredit report under development")

    def import_customers(self):
        """Import customers from file"""
        messagebox.showinfo("قريباً - Coming Soon", "استيراد العملاء قيد التطوير\nCustomer import under development")

    def export_customers(self):
        """Export customers to file"""
        messagebox.showinfo("قريباً - Coming Soon", "تصدير العملاء قيد التطوير\nCustomer export under development")

    def run(self):
        """Run the customers window"""
        if self.window:
            self.window.mainloop()
