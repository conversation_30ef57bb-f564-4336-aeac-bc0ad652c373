#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reports window for Qatar POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import csv
from pathlib import Path

from core.reports_manager import ReportsManager
from core.auth import auth_manager
from config.settings import Settings
from utils.logger import get_logger

class ReportsWindow:
    """Reports and analytics window"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.reports_manager = ReportsManager()
        self.settings = Settings()
        self.logger = get_logger()
        
        self.window = None
        self.current_report_data = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the reports interface"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("التقارير والتحليلات - Reports & Analytics")
        self.window.geometry("1200x800")
        
        # Create main layout
        self.create_sidebar()
        self.create_main_content()
        self.create_status_bar()
        
    def create_sidebar(self):
        """Create sidebar with report types"""
        sidebar_frame = ttk.Frame(self.window, width=250)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(5, 0), pady=5)
        sidebar_frame.pack_propagate(False)
        
        # Report types
        reports_frame = ttk.LabelFrame(sidebar_frame, text="أنواع التقارير - Report Types", padding="10")
        reports_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Sales reports
        sales_frame = ttk.LabelFrame(reports_frame, text="تقارير المبيعات - Sales Reports", padding="5")
        sales_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(sales_frame, text="تقرير المبيعات اليومي\nDaily Sales Report", 
                  command=lambda: self.generate_sales_report('day')).pack(fill=tk.X, pady=2)
        ttk.Button(sales_frame, text="تقرير المبيعات الشهري\nMonthly Sales Report", 
                  command=lambda: self.generate_sales_report('month')).pack(fill=tk.X, pady=2)
        ttk.Button(sales_frame, text="تقرير المنتجات\nProduct Sales Report", 
                  command=self.generate_product_report).pack(fill=tk.X, pady=2)
        
        # Financial reports
        if auth_manager.can_access_feature('financial_reports'):
            financial_frame = ttk.LabelFrame(reports_frame, text="التقارير المالية - Financial Reports", padding="5")
            financial_frame.pack(fill=tk.X, pady=(0, 10))
            
            ttk.Button(financial_frame, text="تقرير الأرباح والخسائر\nProfit & Loss Report", 
                      command=self.generate_profit_report).pack(fill=tk.X, pady=2)
        
        # Customer reports
        customer_frame = ttk.LabelFrame(reports_frame, text="تقارير العملاء - Customer Reports", padding="5")
        customer_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(customer_frame, text="تحليل العملاء\nCustomer Analysis", 
                  command=self.generate_customer_report).pack(fill=tk.X, pady=2)
        
        # Inventory reports
        if auth_manager.can_access_feature('inventory'):
            inventory_frame = ttk.LabelFrame(reports_frame, text="تقارير المخزون - Inventory Reports", padding="5")
            inventory_frame.pack(fill=tk.X, pady=(0, 10))
            
            ttk.Button(inventory_frame, text="حالة المخزون\nInventory Status", 
                      command=self.generate_inventory_report).pack(fill=tk.X, pady=2)
        
        # Date range selection
        date_frame = ttk.LabelFrame(sidebar_frame, text="نطاق التاريخ - Date Range", padding="10")
        date_frame.pack(fill=tk.X, pady=(0, 10))
        
        # From date
        ttk.Label(date_frame, text="من - From:").pack(anchor=tk.W)
        self.from_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        ttk.Entry(date_frame, textvariable=self.from_date_var, width=20).pack(fill=tk.X, pady=(0, 5))
        
        # To date
        ttk.Label(date_frame, text="إلى - To:").pack(anchor=tk.W)
        self.to_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        ttk.Entry(date_frame, textvariable=self.to_date_var, width=20).pack(fill=tk.X, pady=(0, 10))
        
        # Quick date buttons
        ttk.Button(date_frame, text="اليوم - Today", 
                  command=self.set_today).pack(fill=tk.X, pady=1)
        ttk.Button(date_frame, text="هذا الأسبوع - This Week", 
                  command=self.set_this_week).pack(fill=tk.X, pady=1)
        ttk.Button(date_frame, text="هذا الشهر - This Month", 
                  command=self.set_this_month).pack(fill=tk.X, pady=1)
        
        # Export options
        export_frame = ttk.LabelFrame(sidebar_frame, text="تصدير - Export", padding="10")
        export_frame.pack(fill=tk.X)
        
        ttk.Button(export_frame, text="تصدير CSV - Export CSV", 
                  command=self.export_csv).pack(fill=tk.X, pady=2)
        ttk.Button(export_frame, text="تصدير JSON - Export JSON", 
                  command=self.export_json).pack(fill=tk.X, pady=2)
        ttk.Button(export_frame, text="طباعة - Print", 
                  command=self.print_report).pack(fill=tk.X, pady=2)
        
    def create_main_content(self):
        """Create main content area"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Report title
        self.report_title_var = tk.StringVar(value="اختر نوع التقرير - Select Report Type")
        title_label = ttk.Label(main_frame, textvariable=self.report_title_var, 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Report content notebook
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Summary tab
        self.summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.summary_frame, text="الملخص - Summary")
        
        # Details tab
        self.details_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.details_frame, text="التفاصيل - Details")
        
        # Charts tab (placeholder)
        self.charts_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.charts_frame, text="الرسوم البيانية - Charts")
        
        # Initialize with welcome message
        self.show_welcome_message()
        
    def create_status_bar(self):
        """Create status bar"""
        status_frame = ttk.Frame(self.window)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar(value="جاهز - Ready")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # Report generation time
        self.generation_time_var = tk.StringVar(value="")
        ttk.Label(status_frame, textvariable=self.generation_time_var).pack(side=tk.RIGHT, padx=5)
        
    def show_welcome_message(self):
        """Show welcome message"""
        # Clear existing content
        for widget in self.summary_frame.winfo_children():
            widget.destroy()
        for widget in self.details_frame.winfo_children():
            widget.destroy()
        for widget in self.charts_frame.winfo_children():
            widget.destroy()
            
        # Welcome message
        welcome_text = """
مرحباً بك في نظام التقارير والتحليلات

Welcome to Reports & Analytics System

اختر نوع التقرير من القائمة الجانبية لبدء التحليل
Select a report type from the sidebar to start analysis

التقارير المتاحة:
Available Reports:
• تقارير المبيعات اليومية والشهرية
• تحليل المنتجات والفئات  
• تقارير الأرباح والخسائر
• تحليل العملاء
• حالة المخزون

• Daily and Monthly Sales Reports
• Product and Category Analysis
• Profit & Loss Reports
• Customer Analysis
• Inventory Status
        """
        
        welcome_label = ttk.Label(self.summary_frame, text=welcome_text, 
                                justify=tk.CENTER, font=("Arial", 12))
        welcome_label.pack(expand=True)
        
    def set_today(self):
        """Set date range to today"""
        today = datetime.now().strftime('%Y-%m-%d')
        self.from_date_var.set(today)
        self.to_date_var.set(today)
        
    def set_this_week(self):
        """Set date range to this week"""
        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday())
        self.from_date_var.set(start_of_week.strftime('%Y-%m-%d'))
        self.to_date_var.set(today.strftime('%Y-%m-%d'))
        
    def set_this_month(self):
        """Set date range to this month"""
        today = datetime.now()
        start_of_month = today.replace(day=1)
        self.from_date_var.set(start_of_month.strftime('%Y-%m-%d'))
        self.to_date_var.set(today.strftime('%Y-%m-%d'))
        
    def generate_sales_report(self, group_by='day'):
        """Generate sales report"""
        try:
            self.status_var.set("جاري إنشاء تقرير المبيعات... - Generating sales report...")
            self.window.update()
            
            start_date = self.from_date_var.get()
            end_date = self.to_date_var.get()
            
            # Generate report
            report_data = self.reports_manager.get_sales_report(start_date, end_date, group_by)
            self.current_report_data = report_data
            
            # Update title
            title = f"تقرير المبيعات - Sales Report ({start_date} to {end_date})"
            self.report_title_var.set(title)
            
            # Display report
            self.display_sales_report(report_data)
            
            self.status_var.set("تم إنشاء تقرير المبيعات - Sales report generated")
            self.generation_time_var.set(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            self.logger.error(f"Error generating sales report: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في إنشاء التقرير\nError generating report: {str(e)}")
            self.status_var.set("خطأ في إنشاء التقرير - Error generating report")
            
    def display_sales_report(self, report_data):
        """Display sales report data"""
        # Clear existing content
        for widget in self.summary_frame.winfo_children():
            widget.destroy()
        for widget in self.details_frame.winfo_children():
            widget.destroy()
            
        # Summary tab
        summary_text = f"""
إجمالي الفواتير: {report_data['summary']['total_invoices'] or 0}
إجمالي المبيعات: {report_data['summary']['total_sales'] or 0:.2f} QAR
متوسط قيمة الفاتورة: {report_data['summary']['avg_invoice_value'] or 0:.2f} QAR
إجمالي الخصومات: {report_data['summary']['total_discount'] or 0:.2f} QAR
إجمالي الضرائب: {report_data['summary']['total_tax'] or 0:.2f} QAR

Total Invoices: {report_data['summary']['total_invoices'] or 0}
Total Sales: {report_data['summary']['total_sales'] or 0:.2f} QAR
Average Invoice Value: {report_data['summary']['avg_invoice_value'] or 0:.2f} QAR
Total Discounts: {report_data['summary']['total_discount'] or 0:.2f} QAR
Total Tax: {report_data['summary']['total_tax'] or 0:.2f} QAR
        """
        
        summary_label = ttk.Label(self.summary_frame, text=summary_text, 
                                justify=tk.LEFT, font=("Arial", 12))
        summary_label.pack(anchor=tk.W, padx=20, pady=20)
        
        # Payment methods breakdown
        if report_data['payment_methods']:
            payment_frame = ttk.LabelFrame(self.summary_frame, text="طرق الدفع - Payment Methods", padding="10")
            payment_frame.pack(fill=tk.X, padx=20, pady=10)
            
            for method in report_data['payment_methods']:
                method_text = f"{method['payment_method']}: {method['count']} invoices, {method['total']:.2f} QAR"
                ttk.Label(payment_frame, text=method_text).pack(anchor=tk.W)
        
        # Details tab - Sales data table
        if report_data['sales_data']:
            columns = list(report_data['sales_data'][0].keys())
            tree = ttk.Treeview(self.details_frame, columns=columns, show='headings')
            
            for col in columns:
                tree.heading(col, text=col.replace('_', ' ').title())
                tree.column(col, width=100)
            
            for row in report_data['sales_data']:
                values = [str(row.get(col, '')) for col in columns]
                tree.insert('', tk.END, values=values)
            
            # Scrollbars
            v_scrollbar = ttk.Scrollbar(self.details_frame, orient=tk.VERTICAL, command=tree.yview)
            h_scrollbar = ttk.Scrollbar(self.details_frame, orient=tk.HORIZONTAL, command=tree.xview)
            tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
            
            tree.grid(row=0, column=0, sticky='nsew')
            v_scrollbar.grid(row=0, column=1, sticky='ns')
            h_scrollbar.grid(row=1, column=0, sticky='ew')
            
            self.details_frame.grid_rowconfigure(0, weight=1)
            self.details_frame.grid_columnconfigure(0, weight=1)
            
    def generate_product_report(self):
        """Generate product sales report"""
        try:
            self.status_var.set("جاري إنشاء تقرير المنتجات... - Generating product report...")
            self.window.update()
            
            start_date = self.from_date_var.get()
            end_date = self.to_date_var.get()
            
            report_data = self.reports_manager.get_product_sales_report(start_date, end_date)
            self.current_report_data = report_data
            
            title = f"تقرير المنتجات - Product Report ({start_date} to {end_date})"
            self.report_title_var.set(title)
            
            self.display_product_report(report_data)
            
            self.status_var.set("تم إنشاء تقرير المنتجات - Product report generated")
            self.generation_time_var.set(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            self.logger.error(f"Error generating product report: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في إنشاء التقرير\nError generating report: {str(e)}")
            
    def display_product_report(self, report_data):
        """Display product report data"""
        # Clear existing content
        for widget in self.summary_frame.winfo_children():
            widget.destroy()
        for widget in self.details_frame.winfo_children():
            widget.destroy()
            
        # Top products summary
        if report_data['top_products_by_sales']:
            top_products_frame = ttk.LabelFrame(self.summary_frame, text="أفضل المنتجات مبيعاً - Top Selling Products", padding="10")
            top_products_frame.pack(fill=tk.X, padx=20, pady=10)
            
            for i, product in enumerate(report_data['top_products_by_sales'][:5], 1):
                product_text = f"{i}. {product['name_en']}: {product['total_sales']:.2f} QAR"
                ttk.Label(top_products_frame, text=product_text).pack(anchor=tk.W)
        
        # Category performance
        if report_data['category_performance']:
            category_frame = ttk.LabelFrame(self.summary_frame, text="أداء الفئات - Category Performance", padding="10")
            category_frame.pack(fill=tk.X, padx=20, pady=10)
            
            for category in report_data['category_performance'][:5]:
                category_text = f"{category['category_name_en'] or 'Uncategorized'}: {category['total_sales']:.2f} QAR"
                ttk.Label(category_frame, text=category_text).pack(anchor=tk.W)
        
        # Details tab
        if report_data['top_products_by_sales']:
            columns = list(report_data['top_products_by_sales'][0].keys())
            tree = ttk.Treeview(self.details_frame, columns=columns, show='headings')
            
            for col in columns:
                tree.heading(col, text=col.replace('_', ' ').title())
                tree.column(col, width=100)
            
            for row in report_data['top_products_by_sales']:
                values = [str(row.get(col, '')) for col in columns]
                tree.insert('', tk.END, values=values)
            
            # Scrollbars
            v_scrollbar = ttk.Scrollbar(self.details_frame, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=v_scrollbar.set)
            
            tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
    # Placeholder methods for other reports
    def generate_profit_report(self):
        """Generate profit report"""
        try:
            # Clear previous results
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            # Get date range
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            if not start_date or not end_date:
                messagebox.showerror("خطأ - Error", "يرجى تحديد تاريخ البداية والنهاية\nPlease select start and end dates")
                return

            # Generate profit report
            report = self.reports_manager.get_profit_report(start_date, end_date)

            if not report:
                messagebox.showwarning("تحذير - Warning", "لا توجد بيانات للفترة المحددة\nNo data found for selected period")
                return

            # Configure columns for profit report
            self.results_tree['columns'] = ('metric', 'amount', 'percentage')
            self.results_tree['show'] = 'headings'

            self.results_tree.heading('metric', text='البند - Metric')
            self.results_tree.heading('amount', text='المبلغ - Amount (QAR)')
            self.results_tree.heading('percentage', text='النسبة - Percentage')

            self.results_tree.column('metric', width=200)
            self.results_tree.column('amount', width=150, anchor=tk.E)
            self.results_tree.column('percentage', width=100, anchor=tk.E)

            # Add profit data
            profit_data = [
                ('إجمالي المبيعات - Total Sales', f"{report['total_sales']:,.2f}", "100.0%"),
                ('تكلفة البضاعة - Cost of Goods', f"{report['total_cost']:,.2f}", f"{(report['total_cost']/report['total_sales']*100):,.1f}%"),
                ('إجمالي الربح - Gross Profit', f"{report['gross_profit']:,.2f}", f"{(report['gross_profit']/report['total_sales']*100):,.1f}%"),
                ('الخصومات - Discounts', f"{report['total_discounts']:,.2f}", f"{(report['total_discounts']/report['total_sales']*100):,.1f}%"),
                ('صافي الربح - Net Profit', f"{report['net_profit']:,.2f}", f"{(report['net_profit']/report['total_sales']*100):,.1f}%")
            ]

            for metric, amount, percentage in profit_data:
                self.results_tree.insert('', tk.END, values=(metric, amount, percentage))

            # Update summary
            self.update_summary_text(f"""
تقرير الأرباح والخسائر - Profit & Loss Report
الفترة: {start_date} إلى {end_date}
Period: {start_date} to {end_date}

إجمالي المبيعات: {report['total_sales']:,.2f} ر.ق
Total Sales: QAR {report['total_sales']:,.2f}

تكلفة البضاعة: {report['total_cost']:,.2f} ر.ق
Cost of Goods: QAR {report['total_cost']:,.2f}

إجمالي الربح: {report['gross_profit']:,.2f} ر.ق
Gross Profit: QAR {report['gross_profit']:,.2f}

هامش الربح الإجمالي: {(report['gross_profit']/report['total_sales']*100):,.1f}%
Gross Profit Margin: {(report['gross_profit']/report['total_sales']*100):,.1f}%

صافي الربح: {report['net_profit']:,.2f} ر.ق
Net Profit: QAR {report['net_profit']:,.2f}

هامش الربح الصافي: {(report['net_profit']/report['total_sales']*100):,.1f}%
Net Profit Margin: {(report['net_profit']/report['total_sales']*100):,.1f}%
            """)

            self.logger.info(f"Profit report generated for period {start_date} to {end_date}")

        except Exception as e:
            self.logger.error(f"Error generating profit report: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في إنشاء تقرير الأرباح\nError generating profit report: {str(e)}")
        
    def generate_customer_report(self):
        """Generate customer report"""
        try:
            # Clear previous results
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            # Get date range
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            if not start_date or not end_date:
                messagebox.showerror("خطأ - Error", "يرجى تحديد تاريخ البداية والنهاية\nPlease select start and end dates")
                return

            # Generate customer report
            report = self.reports_manager.get_customer_report(start_date, end_date)

            if not report or not report.get('customers'):
                messagebox.showwarning("تحذير - Warning", "لا توجد بيانات عملاء للفترة المحددة\nNo customer data found for selected period")
                return

            # Configure columns for customer report
            self.results_tree['columns'] = ('customer', 'invoices', 'total_amount', 'avg_amount')
            self.results_tree['show'] = 'headings'

            self.results_tree.heading('customer', text='العميل - Customer')
            self.results_tree.heading('invoices', text='عدد الفواتير - Invoices')
            self.results_tree.heading('total_amount', text='إجمالي المبلغ - Total Amount')
            self.results_tree.heading('avg_amount', text='متوسط الفاتورة - Avg Invoice')

            self.results_tree.column('customer', width=200)
            self.results_tree.column('invoices', width=100, anchor=tk.CENTER)
            self.results_tree.column('total_amount', width=120, anchor=tk.E)
            self.results_tree.column('avg_amount', width=120, anchor=tk.E)

            # Add customer data
            for customer in report['customers']:
                self.results_tree.insert('', tk.END, values=(
                    customer['name'],
                    customer['invoice_count'],
                    f"{customer['total_amount']:,.2f}",
                    f"{customer['avg_amount']:,.2f}"
                ))

            # Update summary
            self.update_summary_text(f"""
تقرير العملاء - Customer Report
الفترة: {start_date} إلى {end_date}
Period: {start_date} to {end_date}

إجمالي العملاء: {report['total_customers']}
Total Customers: {report['total_customers']}

إجمالي الفواتير: {report['total_invoices']}
Total Invoices: {report['total_invoices']}

إجمالي المبيعات: {report['total_sales']:,.2f} ر.ق
Total Sales: QAR {report['total_sales']:,.2f}

متوسط قيمة الفاتورة: {report['avg_invoice_amount']:,.2f} ر.ق
Average Invoice Amount: QAR {report['avg_invoice_amount']:,.2f}
            """)

            self.logger.info(f"Customer report generated for period {start_date} to {end_date}")

        except Exception as e:
            self.logger.error(f"Error generating customer report: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في إنشاء تقرير العملاء\nError generating customer report: {str(e)}")
        
    def generate_inventory_report(self):
        """Generate inventory report"""
        messagebox.showinfo("قريباً - Coming Soon", "تقرير المخزون قيد التطوير\nInventory report under development")
        
    def export_csv(self):
        """Export current report to CSV"""
        if not self.current_report_data:
            messagebox.showwarning("تحذير - Warning", "لا يوجد تقرير لتصديره\nNo report to export")
            return
            
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if filename:
                # Export main data (simplified)
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    if 'sales_data' in self.current_report_data:
                        data = self.current_report_data['sales_data']
                    elif 'top_products_by_sales' in self.current_report_data:
                        data = self.current_report_data['top_products_by_sales']
                    else:
                        data = []
                        
                    if data:
                        writer = csv.DictWriter(csvfile, fieldnames=data[0].keys())
                        writer.writeheader()
                        writer.writerows(data)
                        
                messagebox.showinfo("نجح - Success", f"تم تصدير التقرير\nReport exported: {filename}")
                
        except Exception as e:
            self.logger.error(f"Error exporting CSV: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في التصدير\nExport error: {str(e)}")
            
    def export_json(self):
        """Export current report to JSON"""
        if not self.current_report_data:
            messagebox.showwarning("تحذير - Warning", "لا يوجد تقرير لتصديره\nNo report to export")
            return
            
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as jsonfile:
                    json.dump(self.current_report_data, jsonfile, ensure_ascii=False, indent=2)
                    
                messagebox.showinfo("نجح - Success", f"تم تصدير التقرير\nReport exported: {filename}")
                
        except Exception as e:
            self.logger.error(f"Error exporting JSON: {e}")
            messagebox.showerror("خطأ - Error", f"خطأ في التصدير\nExport error: {str(e)}")
            
    def print_report(self):
        """Print current report"""
        messagebox.showinfo("قريباً - Coming Soon", "طباعة التقارير قيد التطوير\nReport printing under development")
        
    def run(self):
        """Run the reports window"""
        if self.window:
            self.window.mainloop()
